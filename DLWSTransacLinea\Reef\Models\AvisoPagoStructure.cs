﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Models
{
    public class AvisoPagoStructure
    {
        public int cmpVal { get; set; }
        public string pymDcmTypVal { get; set; }
        public string pymDcmTypNam { get; set; }
        public string pymDcmVal { get; set; }
        public int mvmVal { get; set; }
        public long mvmDat { get; set; }
        public long expDat { get; set; }
        public int crnVal { get; set; }
        public string crnNam { get; set; }
        /// <summary>
        /// Importe del aviso de pago
        /// </summary>
        public double dcmAmn { get; set; }
        public int exrVal { get; set; }
        public int thpAcvVal { get; set; }
        public string thpDcmTypVal { get; set; }
        public string thpDcmVal { get; set; }
        public string rcrStsTypVal { get; set; }
        public int delVal { get; set; }
        public string gppVal { get; set; }
        public string mnrTypVal { get; set; }
        public string mnrTypNam { get; set; }
        public string mnrVal { get; set; }
        public string mnrCpeNam { get; set; }
        public string rcrStsTypNam { get; set; }
    }
}
