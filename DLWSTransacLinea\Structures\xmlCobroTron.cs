﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Structures
{
    public class xmlCobroTron
    {
        #region Public Fields

        public List<requerimiento> requerimientos;
        public List<documento_ingreso> tiposPago;

        #endregion Public Fields

        #region Public Properties

        public _ENCABEZADO encabezado { get; set; }

        #endregion Public Properties

        #region Public Methods

        public string generarXMLCobro(xmlCobroTron datos)
        {
            var xmlfromLINQ = new XElement("DATOS",
                        new XElement("ENCABEZADO",

                            new XElement("SISTEMA", datos.encabezado.SISTEMA),
                            new XElement("EQUIPO", datos.encabezado.EQUIPO),
                            new XElement("NUMEPAGO", datos.encabezado.NUMEPAGO)),

                        //RECIBOS TRON WEB
                        from c in datos.requerimient<PERSON>
                        select new XElement("REQUERIMIENTO",

                                new XElement("NUMRECI", c.NUMRECI),
                                new XElement("SERIE", c.SERIE),
                                new XElement("NUMEFACT", c.NUMEFACT)),

                        //DOCUMENTOS DE PAGO TRON WEB
                        from t in datos.tiposPago
                        select new XElement("DOCING",

                                new XElement("TIPOPAGO", t.TIPOPAGO),
                                new XElement("ENTFINAN", t.ENTFINAN),
                                new XElement("NUMREF", t.NUMREF),
                                new XElement("CODMONEDA", t.CODMONEDA),
                                new XElement("MONTO", t.MONTO),
                                new XElement("NUMAUTORIZA", t.NUMAUTORIZA),
                                new XElement("FECHA_CHQ", t.FECHA_CHQ),
                                new XElement("TIPO_TAR", t.TIPO_TAR),
                                new XElement("CODIGO_TAR", t.CODIGO_TAR),
                                new XElement("NUMREF_TAR", t.NUMREF_TAR))

                );

            return xmlfromLINQ.ToString();
        }

        public string generarXMLCobroV2(xmlCobroTron datos)
        {
            var xmlfromLINQ = new XElement("PAGO",

                        new XElement("ENCABEZADO",

                            new XElement("EQUIPO", datos.encabezado.EQUIPO),
                            new XElement("USUARIO_ORIGEN", datos.encabezado.USUARIO_ORIGEN),
                            new XElement("CODIGO_CAJERO", datos.encabezado.CODIGO_CAJERO),
                            new XElement("ES_AVISO", datos.encabezado.ES_AVISO)),

                        new XElement("REQUERIMIENTOS",
                            from c in datos.requerimientos
                            select new XElement("REQUERIMIENTO", c.NUMRECI)),

                        new XElement("TIPO_PAGO",
                            from t in datos.tiposPago
                            select new XElement("TIPO",
                                    new XElement("CODIGO", t.TIPOPAGO),
                                    new XElement("MONEDA", t.CODMONEDA),
                                    new XElement("MONTO", t.MONTO),
                                    new XElement("CODENTFINAN", t.ENTFINAN),
                                    new XElement("NUMREFDOC", t.NUMREF),
                                    new XElement("NUMAUTORIZA", t.NUMAUTORIZA),
                                    new XElement("FECHA_CHQ", t.FECHA_CHQ),
                                    new XElement("TIPO_TAR", t.TIPO_TAR),
                                    new XElement("CODIGO_TAR", t.CODIGO_TAR),
                                    new XElement("NUMREF_TAR", t.NUMREF_TAR),
                                    new XElement("CUENTA_SIMPLIFICADA", t.CUENTA_SIMPLIFICADA),
                                    new XElement("DESCRIPCION", t.DESCRIPCION))));

            return xmlfromLINQ.ToString();
        }

        #endregion Public Methods

        #region Public Classes

        public class _ENCABEZADO
        {
            #region Public Properties

            public string CODIGO_CAJERO { get; set; }
            public string EQUIPO { get; set; }
            public string NUMEPAGO { get; set; }
            public string SISTEMA { get; set; }
            public string USUARIO_ORIGEN { get; set; }
            public string ES_AVISO { get; set; }

            #endregion Public Properties
        }

        public class documento_ingreso
        {
            #region Public Properties

            public string CODIGO_TAR { get; set; }
            public string CODMONEDA { get; set; }
            public string CUENTA_SIMPLIFICADA { get; set; }
            public string DESCRIPCION { get; set; }
            public string ENTFINAN { get; set; }
            public string FECHA_CHQ { get; set; }
            public string MONTO { get; set; }
            public string NUMAUTORIZA { get; set; }
            public string NUMREF { get; set; }
            public string NUMREF_TAR { get; set; }
            public string TIPO_TAR { get; set; }
            public string TIPOPAGO { get; set; }

            #endregion Public Properties
        }

        public class requerimiento
        {
            #region Public Properties

            public string NUMEFACT { get; set; }
            public string NUMRECI { get; set; }
            public string SERIE { get; set; }            

            #endregion Public Properties
        }

        #endregion Public Classes
    }
}