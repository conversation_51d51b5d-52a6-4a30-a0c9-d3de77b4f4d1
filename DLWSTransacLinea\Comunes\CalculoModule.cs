﻿using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Structures;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Comunes
{
    public class CalculoModule
    {
        /// <summary>
        /// Proceso para calcular
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <returns></returns>
        public XElement CalcularTransaccion(xmlTransaccion._transaccion xmlSolicitud)
        {
            XElement resultado = null;
            xmlTransaccion clRespuesta = new xmlTransaccion();

            DLWSTransacLinea.Cores.MySQLLogger Oper = new Cores.MySQLLogger();

            #region INIZIALIZACION
            xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
            clTransaccion.datos = new xmlTransaccion._estructuraTransaccion();
            clTransaccion.datos.encabezado = new xmlTransaccion._encabezado();
            clTransaccion.datos.polizas = new xmlTransaccion._poliza();
            clTransaccion.datos.polizas.poliza = new List<xmlTransaccion.poliza>();
            clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
            clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();
            clTransaccion.datos.identificadores = new xmlTransaccion._identificadores();
            clTransaccion.datos.mediosPago = new xmlTransaccion._medio();
            clTransaccion.datos.mediosPago.medio = new List<xmlTransaccion.medio>();
            #endregion INIZIALIZACION

            #region ASIGNACION
            clTransaccion.datos.encabezado = xmlSolicitud.datos.encabezado;
            clTransaccion.datos.identificadores = xmlSolicitud.datos.identificadores;
            #endregion ASIGNACION

            Cores.AcselDB loggerA = new Cores.AcselDB();
            #region TIPO | BUSQUEDA

            switch (xmlSolicitud.datos.encabezado.tipoBusqueda)
            {
                case "LIQUIDACION":
                    if (!string.IsNullOrWhiteSpace(xmlSolicitud.datos.identificadores.num_sini))
                    {
                        DeducibleReef deducibleReef = new DeducibleReef();
                        Cores.TronwebDB tronwebDB = new Cores.TronwebDB();

                        DataTable dtCalculos = new DataTable();

                        switch (xmlSolicitud.datos.identificadores.sistema)
                        {
                            case "T":
                                dtCalculos = tronwebDB.CalcularLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini,
                                                                           p_mca_factura: xmlSolicitud.datos.identificadores.mca_factura,
                                                                           p_num_cuota: xmlSolicitud.datos.identificadores.num_cuotas);
                                break;
                            case "R":
                                dtCalculos = deducibleReef.CalcularLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini,
                                                                              p_mca_factura: xmlSolicitud.datos.identificadores.mca_factura,
                                                                              p_num_cuota: xmlSolicitud.datos.identificadores.num_cuotas);
                                break;
                        }

                        if (dtCalculos.Rows.Count > 0)
                        {
                            for (int i = 0; i < dtCalculos.Rows.Count; i++)
                            {
                                clTransaccion.datos.liquidaciones = new xmlTransaccion._liquidacion();
                                clTransaccion.datos.liquidaciones.liquidacion = new List<xmlTransaccion.liquidacion>();
                                float l_monto_dedu = 0, l_monto_total = 0;
                                try { l_monto_dedu = Math.Abs(float.Parse(dtCalculos.Rows[i]["mto_deducible"].ToString())); } catch (Exception ex) { }
                                try { l_monto_total = Math.Abs(float.Parse(dtCalculos.Rows[i]["mto_total"].ToString())); } catch (Exception ex) { }
                                clTransaccion.datos.liquidaciones.liquidacion.Add(new xmlTransaccion.liquidacion()
                                {
                                    moneda = dtCalculos.Rows[i]["moneda"].ToString(),
                                    mto_deducible = l_monto_dedu.ToString(),
                                    mto_iva = Math.Abs(float.Parse(dtCalculos.Rows[i]["mto_iva"].ToString())).ToString(),
                                    mto_cuotas = Math.Abs(float.Parse(dtCalculos.Rows[i]["mto_cuotas"].ToString())).ToString(),
                                    mto_total = l_monto_total.ToString()
                                });
                            }
                            clTransaccion.datos.encabezado.codigoRetorno = "00";
                            clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                        }
                        else
                        {
                            clTransaccion.datos.encabezado.codigoRetorno = "01";
                            clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                        }
                    }
                    else
                    {
                        clTransaccion.datos.encabezado.codigoRetorno = "01";
                        clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                    }
                    break;
                default:
                    break;
            }
            #endregion TIPO | BUSQUEDA

            #region GENERACION | XML
            resultado = clRespuesta.generarXmlTransaccion(clTransaccion);
            #endregion GENERACION | XML

            return resultado;
        }

    }
}
