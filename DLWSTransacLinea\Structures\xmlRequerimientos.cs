﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Structures
{
    public class xmlRequerimientos
    {
        [XmlElement(ElementName = "Requerimiento")]
        public List<requerimiento> requerimientos = new List<requerimiento>();
        public class requerimiento
        {
            [XmlText]
            public string req { get; set; }
        }
    }
}
