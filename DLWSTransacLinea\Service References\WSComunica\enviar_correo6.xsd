<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System.Globalization" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Globalization" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="CodePageDataItem">
    <xs:sequence>
      <xs:element name="m_bodyName" nillable="true" type="xs:string" />
      <xs:element name="m_dataIndex" type="xs:int" />
      <xs:element name="m_flags" type="xs:unsignedInt" />
      <xs:element name="m_headerName" nillable="true" type="xs:string" />
      <xs:element name="m_uiFamilyCodePage" type="xs:int" />
      <xs:element name="m_webName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CodePageDataItem" nillable="true" type="tns:CodePageDataItem" />
</xs:schema>