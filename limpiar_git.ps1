Write-Host "Limpiando cache de Git para aplicar el nuevo .gitignore..." -ForegroundColor Green

# Añadir el archivo .gitignore modificado
git add .gitignore
git commit -m "Actualizar .gitignore para excluir carpetas bin, obj y .vs"

# Eliminar archivos específicos del índice de Git (sin borrarlos del disco)
Write-Host "Eliminando carpetas del índice de Git..." -ForegroundColor Yellow
git rm -r --cached .vs/
git rm -r --cached DLWSTransacLinea/bin/
git rm -r --cached DLWSTransacLinea/obj/
git rm -r --cached WCFTransacLinea/bin/
git rm -r --cached WCFTransacLinea/obj/

# Añadir todos los archivos de nuevo (respetando el nuevo .gitignore)
Write-Host "Añadiendo archivos de nuevo (respetando el nuevo .gitignore)..." -ForegroundColor Yellow
git add .

# Hacer commit de los cambios
git commit -m "Aplicar nuevo .gitignore a los archivos existentes"

Write-Host "Proceso completado. Las carpetas ahora deberían ser ignoradas por Git." -ForegroundColor Green
Write-Host "Presiona cualquier tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
