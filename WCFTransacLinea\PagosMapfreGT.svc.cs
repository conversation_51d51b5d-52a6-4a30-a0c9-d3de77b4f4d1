﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Cores;
using DLWSTransacLinea.Reef.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using System.Xml.Linq;

namespace WCFTransacLinea
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "PagosMapfreGT" in code, svc and config file together.
    // NOTE: In order to launch WCF Test Client for testing this service, please select PagosMapfreGT.svc or PagosMapfreGT.svc.cs at the Solution Explorer and start debugging.
    public class PagosMapfreGT : IPagosMapfreGT
    {
        #region Public Methods

        public string deserealizarXMLaJson(string origenXml, string raiz, string[] origenListas)
        {
            string respuesta = string.Empty;

            transaccion clTransaccion = new transaccion();

            respuesta = clTransaccion._deserealizarXMLaJson(origenXml, raiz, origenListas);

            return respuesta;
        }

        public XElement ejecutarTransaccion(string xmlDatos)
        {
            XElement resultado = null;
            try
            {
                transaccion clTransaccion = new transaccion();

                resultado = clTransaccion.ejecutaTransaccion(xmlDatos);
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage(ex, "principal [ejecutarTransaccion]");
            }
            return resultado;
        }

        public string generarXmlTransaccion(_transaccion _datos)
        {
            var xmlfromLINQ = new XElement("datos",

                    new XElement("encabezado",
                        new XElement("convenio", _datos.datos.encabezado.convenio),
                        new XElement("proveedor", _datos.datos.encabezado.proveedor),
                        new XElement("codigoRetorno", _datos.datos.encabezado.codigoRetorno),
                        new XElement("mensajeRetorno", _datos.datos.encabezado.mensajeRetorno),
                        new XElement("usuarioOrigen", _datos.datos.encabezado.usuarioOrigen),
                        new XElement("codigoCajero", _datos.datos.encabezado.codigoCajero),
                        new XElement("tipoBusqueda", _datos.datos.encabezado.tipoBusqueda),
                        new XElement("tipoTransaccion", _datos.datos.encabezado.tipoTransaccion),
                        new XElement("autorizacionProveedor", _datos.datos.encabezado.autorizacionProveedor),
                        new XElement("autorizacionBanco", _datos.datos.encabezado.autorizacionBanco),
                        new XElement("idTransaccion", _datos.datos.encabezado.idTransaccion),
                        new XElement("sistemaUso", _datos.datos.encabezado.sistemaUso),
                        new XElement("idReferencia", _datos.datos.encabezado.idReferencia),
                        new XElement("esAviso", _datos.datos.encabezado.esAviso)),

                    new XElement("identificadores",
                        new XElement("requerimiento", _datos.datos.identificadores.requerimiento),
                        new XElement("idepol", _datos.datos.identificadores.idepol),
                        new XElement("codpol", _datos.datos.identificadores.codpol),
                        new XElement("numpol", _datos.datos.identificadores.numpol),
                        new XElement("numcert", _datos.datos.identificadores.numcert),
                        new XElement("dvid", _datos.datos.identificadores.dvid),
                        new XElement("numid", _datos.datos.identificadores.numid),
                        new XElement("nit", _datos.datos.identificadores.nit),
                        new XElement("sistema", _datos.datos.identificadores.sistema),
                        new XElement("vigencia_inicial", _datos.datos.identificadores.vigencia_inicial),
                        new XElement("es_poliza_grupo", _datos.datos.identificadores.es_poliza_grupo),
                        new XElement("tip_docum", _datos.datos.identificadores.tip_docum),
                        new XElement("num_sini", _datos.datos.identificadores.num_sini),
                        new XElement("mca_factura", _datos.datos.identificadores.mca_factura),
                        new XElement("num_cuotas", _datos.datos.identificadores.num_cuotas)
                        ),

                    new XElement("polizas",
                        ((_datos.datos.polizas != null) ?
                        from p in _datos.datos.polizas.poliza
                        select new XElement("poliza",
                            new XElement("numPoliza", p.numPoliza),
                            new XElement("asegurado", p.asegurado),
                            new XElement("estadoPoliza", p.estadoPoliza),
                            new XElement("inicioVigencia", p.inicioVigencia),
                            new XElement("finVigencia", p.finVigencia),
                            new XElement("certificado", p.certificado),
                            new XElement("idepol", p.idepol),
                            new XElement("sistema", p.sistema),
                            new XElement("mca_provisional", p.mca_provisional),
                            new XElement("poliza_grupo", p.poliza_grupo),
                            new XElement("num_contrato", p.num_contrato),
                            new XElement("tip_docum", p.tip_docum),
                            new XElement("cod_docum", p.cod_docum)

                            ) : null)),

                    new XElement("requerimientos",
                        ((_datos.datos.requerimientos != null) ?
                        from r in _datos.datos.requerimientos.requerimiento
                        select new XElement("requerimiento",
                            new XElement("numeroRequerimiento", r.numeroRequerimiento),
                            new XElement("id_moneda", r.id_moneda),
                            new XElement("moneda", r.moneda),
                            new XElement("totalRequerimiento", r.totalRequerimiento),
                            new XElement("vencimientoRequerimiento", r.vencimientoRequerimiento),
                            new XElement("tip_docum", r.tip_docum),
                            new XElement("cod_docum", r.cod_docum),
                            new XElement("nombrePagador", r.nombrePagador),
                            new XElement("sistema", r.sistema),
                            new XElement("nitPagador", r.nitPagador),
                            new XElement("poliza", r.poliza),
                            new XElement("estado", r.estado),
                            new XElement("numeroCuota", r.numeroCuota),
                            new XElement("idepol", r.idepol),
                            new XElement("fechaCobroRequerimiento", r.fechaCobroRequerimiento),
                            new XElement("asignaFactura", r.asignaFactura),
                            new XElement("codFact", r.codFact),
                            new XElement("email", r.email),
                            new XElement("direc_cobro", r.direc_cobro),
                            new XElement("es_aviso", r.es_aviso)
                            ) : null)),

                    new XElement("liquidaciones",
                        ((_datos.datos.liquidaciones != null) ?
                        from l in _datos.datos.liquidaciones.liquidacion
                        select new XElement("liquidacion",
                            new XElement("id_moneda", l.id_moneda),
                            new XElement("moneda", l.moneda),
                             new XElement("mto_deducible", l.mto_deducible),
                             new XElement("mto_iva", l.mto_iva),
                             new XElement("mto_cuotas", l.mto_cuotas),
                             new XElement("mto_total", l.mto_total),
                             new XElement("num_deducible", l.num_deducible)
                            ) : null)),

                    new XElement("mediosPago",
                        ((_datos.datos.mediosPago != null) ?
                         from m in _datos.datos.mediosPago.medio
                         select new XElement("medio",

                             new XElement("tipoPago", m.tipoPago),
                             new XElement("descEntidadFinanciera", m.descEntidadFinanciera),
                             new XElement("codEntidadFinanciera", m.codEntidadFinanciera),
                             new XElement("numeroReferencia", m.numeroReferencia),
                             new XElement("codMoneda", m.codMoneda),
                             new XElement("monto", m.monto),
                             new XElement("numeroAutorizacion", m.numeroAutorizacion),
                             new XElement("fechaDeposito", m.fechaDeposito),
                             new XElement("numeroDocumento", m.numeroDocumento),
                             new XElement("fecha_cheque", m.fecha_cheque),
                             new XElement("tipoTarjeta", m.tipoTarjeta),
                             new XElement("codigoTarjeta", m.codigoTarjeta),
                             new XElement("CUENTA_SIMPLIFICADA", m.CUENTA_SIMPLIFICADA),
                             new XElement("DESCRIPCION", m.DESCRIPCION)) : null)),

                    new XElement("resumenPago",
                            new XElement("requerimientos",
                            ((_datos.datos.requerimientos != null) ?
                             from res in _datos.datos.requerimientos.requerimiento
                             select new XElement("requerimiento",
                                new XElement("numeroRequerimiento", res.numeroRequerimiento),
                                new XElement("codFact", res.codFact),
                                new XElement("numFact", res.numFact),
                                new XElement("cae", res.cae),
                                new XElement("reling", res.reling)
                                ) : null))),
                    new XElement("mensaje", (_datos.datos.mensaje != null)
                                            ? new[]{new XElement("tipo", _datos.datos.mensaje.tipo),
                                                    new XElement("titulo", _datos.datos.mensaje.titulo),
                                                    new XElement("mensaje", _datos.datos.mensaje.mensaje),
                                                    new XElement("codigo", _datos.datos.mensaje.codigo)}
                                            : null)
                    );

            return xmlfromLINQ.ToString();
        }

        public string sistemaRequerimiento(string requerimiento)
        {
            string respuesta = string.Empty;

            AcselDB clConsultas = new AcselDB();

            respuesta = clConsultas.sisRequerimiento(requerimiento);

            return respuesta;
        }

        #endregion Public Methods

        #region Liquidacion
        /// <summary>
        /// Proceso que genera el reporte de recibo/factura de una liquidacion en TW
        /// </summary>
        /// <param name="pNumLiquidacion"></param>
        /// <returns></returns>
        public string obtenerDocumentoLiq(string pNumLiquidacion)
        {
            string respuesta = string.Empty;
            DocumentacionReef documentacionReef = new DocumentacionReef();

            respuesta = documentacionReef.obtenerDocumentoLiq(pNumLiquidacion);

            return respuesta;
        }
        #endregion Liquidacion
    }
}