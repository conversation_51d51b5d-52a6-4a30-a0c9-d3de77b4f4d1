<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="enviar_correo" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="BasicHttpsBinding_Ienviar_correo_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:TransportToken>
              <wsp:Policy>
                <sp:HttpsToken RequireClientCertificate="false" />
              </wsp:Policy>
            </sp:TransportToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
          </wsp:Policy>
        </sp:TransportBinding>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd4" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/System.Text" />
      <xsd:import schemaLocation="http://***********/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/System.Globalization" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="Ienviar_correo_doEnviar_InputMessage">
    <wsdl:part name="parameters" element="tns:doEnviar" />
  </wsdl:message>
  <wsdl:message name="Ienviar_correo_doEnviar_OutputMessage">
    <wsdl:part name="parameters" element="tns:doEnviarResponse" />
  </wsdl:message>
  <wsdl:portType name="Ienviar_correo">
    <wsdl:operation name="doEnviar">
      <wsdl:input wsaw:Action="http://tempuri.org/Ienviar_correo/doEnviar" message="tns:Ienviar_correo_doEnviar_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/Ienviar_correo/doEnviarResponse" message="tns:Ienviar_correo_doEnviar_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_Ienviar_correo" type="tns:Ienviar_correo">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="doEnviar">
      <soap:operation soapAction="http://tempuri.org/Ienviar_correo/doEnviar" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="BasicHttpsBinding_Ienviar_correo" type="tns:Ienviar_correo">
    <wsp:PolicyReference URI="#BasicHttpsBinding_Ienviar_correo_policy" />
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="doEnviar">
      <soap:operation soapAction="http://tempuri.org/Ienviar_correo/doEnviar" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="enviar_correo">
    <wsdl:port name="BasicHttpBinding_Ienviar_correo" binding="tns:BasicHttpBinding_Ienviar_correo">
      <soap:address location="http://***********/ws_comunica_d/Wcf/enviar_correo.svc" />
    </wsdl:port>
    <wsdl:port name="BasicHttpsBinding_Ienviar_correo" binding="tns:BasicHttpsBinding_Ienviar_correo">
      <soap:address location="https://app2.mapfre.com.gt/ws_comunica_d/Wcf/enviar_correo.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>