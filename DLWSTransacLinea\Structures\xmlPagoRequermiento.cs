﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Structures
{
    public class xmlPagoRequermiento
    {
        public List<requerimiento> requerimientos;

        public List<documento_ingreso> tiposPago;

        public _encabezado encabezado { get; set; }
        public class requerimiento
        {
            public string req { get; set; }
            public string monto { get; set; }
            public string moneda { get; set; }
            public string poliza { get; set; }
        }
        public class documento_ingreso
        {
            public string CODIGO { get; set; }
            public string MONEDA { get; set; }
            public string MONTO { get; set; }
            public string CODENTFINAN { get; set; }
            public string NUMREFDOC { get; set; }
            public string NUMAUTORIZA { get; set; }
            public string SISTEMA { get; set; }
        }
        public class _encabezado
        {
            public string USUARIO_ORIGEN { get; set; }
            public string CODIGO_CAJERO { get; set; }
        }

        public string generarXMLPago(xmlPagoRequermiento datos)
        {
            var xmlfromLINQ = new XElement("PAGO",
                        new XElement("ENCABEZADO",
                            new XElement("USUARIO_ORIGEN", datos.encabezado.USUARIO_ORIGEN),
                            new XElement("CODIGO_CAJERO", datos.encabezado.CODIGO_CAJERO)),
                        new XElement("REQUERIMIENTOS",
                        from c in datos.requerimientos
                        select new XElement("REQUERIMIENTO", c.req)),
                            new XElement("TIPO_PAGO",
                            from d in datos.tiposPago
                            select new XElement("TIPO",
                                new XElement("CODIGO", d.CODIGO),
                                new XElement("MONEDA", d.MONEDA),
                                new XElement("MONTO", d.MONTO),
                                new XElement("CODENTFINAN", d.CODENTFINAN),
                                new XElement("NUMREFDOC", d.NUMREFDOC),
                                new XElement("NUMAUTORIZA", d.NUMAUTORIZA),
                                new XElement("SISTEMA", d.SISTEMA))));

            return xmlfromLINQ.ToString();
        }
    }
}
