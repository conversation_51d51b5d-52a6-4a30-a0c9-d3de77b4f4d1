﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Cores;
using DLWSTransacLinea.Structures;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea
{
    public class BitacoraPagos : IBitacoraPagos
    {
        /// <summary>
        /// Guarda la bitacora de acciones
        /// </summary>
        /// <param name="p_bitacora"></param>
        /// <returns></returns>
        public Estructuras.Bitacora_wsTransacLinea GuardaBitacora(Estructuras.Bitacora_wsTransacLinea p_bitacora)
        {
            try
            {
                MySQLLogger operaciones = new MySQLLogger();
                p_bitacora = operaciones.Insert_Bitacora(p_bitacora);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return p_bitacora;
        }

        /// <summary>
        /// Guarda el detalle de la bitacora
        /// </summary>
        /// <param name="p_bitacora"></param>
        /// <returns></returns>
        public Estructuras.Bitacora_wsTransacLinea GuardaDetalleBitacora(Estructuras.Bitacora_wsTransacLinea p_bitacora)
        {
            try
            {
                MySQLLogger operaciones = new MySQLLogger();
                p_bitacora = operaciones.Insert_DetalleBitacora(p_bitacora);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return p_bitacora;
        }

        /// <summary>
        /// Actualizacion de requerimientos y medio de pago
        /// </summary>
        /// <param name="pBitacora"></param>
        /// <param name="pRequerimientos"></param>
        /// <param name="pMedios"></param>
        /// <returns></returns>
        public Estructuras.Bitacora_wsTransacLinea Update_Bitacora(Estructuras.Bitacora_wsTransacLinea pBitacora,
                                                                   List<xmlTransaccion.requerimiento> pRequerimientos = null,
                                                                   List<xmlTransaccion.medio> pMedios = null)
        {
            try
            {
                MySQLLogger operaciones = new MySQLLogger();
                pBitacora = operaciones.Update_Bitacora(pBitacora, pRequerimientos, pMedios);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return pBitacora;
        }
    }
}
