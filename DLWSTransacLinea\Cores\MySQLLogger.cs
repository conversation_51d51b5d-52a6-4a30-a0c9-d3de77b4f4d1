﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Structures;
using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Cores
{
    public class MySQLLogger
    {
        public DataTable obtenerErroresTransacciones(string idTransaccion)
        {
            DataTable datos = new DataTable();
            ConexionBD clConexiones = new ConexionBD();
            try
            {
                using (MySqlConnection conn = clConexiones.abrirConexionMysqlLog())
                {
                    MySqlCommand cmd = conn.CreateCommand();
                    string query = "call sp_GetErrorsTransact(@pIdTransaccion)";

                    cmd.CommandText = (query);
                    cmd.Prepare();
                    cmd.Parameters.AddWithValue("@pIdTransaccion", idTransaccion);

                    MySqlDataReader rs = cmd.ExecuteReader();
                    datos.Load(rs);

                    rs.Close();
                    rs.Dispose();
                    cmd.Dispose();
                    rs = null;
                    cmd = null;
                    conn.Close();
                }
            }
            catch (Exception e)
            {
            }

            return datos;
        }

        public Estructuras.Bitacora_wsTransacLinea Insert_Bitacora(Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            ConexionBD conexion = new ConexionBD();
            MySqlConnection conn = new MySqlConnection();
            try
            {
                MySqlCommand cmd = new MySqlCommand();
                conn = conexion.abrirConexionMysqlLog();
                cmd.Connection = conn;
                cmd.CommandText = "fnc_Insert_BitacoraWSTransacLinea";
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("?p_host", MySqlDbType.VarChar).Value = Bitacora.HOST;
                cmd.Parameters.Add("?p_url", MySqlDbType.VarChar).Value = Bitacora.URL;
                cmd.Parameters.Add("?p_mensaje", MySqlDbType.Text).Value = Bitacora.MENSAJE;
                cmd.Parameters.Add("?p_xml_in", MySqlDbType.LongBlob).Value = (Bitacora.XML_IN != null ? Encoding.ASCII.GetBytes(Bitacora.XML_IN) : null);
                cmd.Parameters.Add("?p_metodo", MySqlDbType.VarChar).Value = Bitacora.METODO;
                cmd.Parameters.Add("?p_sistema", MySqlDbType.VarChar).Value = Bitacora.SISTEMA;
                cmd.Parameters.Add("?p_usuario_origen", MySqlDbType.VarChar).Value = Bitacora.USUARIO_ORIGEN;
                cmd.Parameters.Add("?p_codigo_usuario", MySqlDbType.VarChar).Value = Bitacora.CODIGO_USUARIO;
                cmd.Parameters.Add("?p_id_referencia", MySqlDbType.VarChar).Value = Bitacora.ID_REFERENCIA;
                cmd.Parameters.Add("?p_sistema_uso", MySqlDbType.VarChar).Value = Bitacora.SISTEMA_USO;
                cmd.Parameters.Add("?p_email_notifica", MySqlDbType.VarChar).Value = Bitacora.EMAIL_NOTIFICA;
                cmd.Parameters.Add("?p_moneda", MySqlDbType.VarChar).Value = Bitacora.MONEDA;
                cmd.Parameters.Add("?p_total", MySqlDbType.VarChar).Value = Bitacora.TOTAL;
                cmd.Parameters.Add("?Return_value", MySqlDbType.UInt64).Direction = ParameterDirection.ReturnValue;
                cmd.ExecuteNonQuery();
                Bitacora.ID_TRANSACCION = Int64.Parse((string)cmd.Parameters["?Return_value"].Value.ToString());
                conn.Close();

            }
            catch (Exception e)
            {
                Logger.LogErrorMessage(e, "Insert_Bitacora");
                Bitacora.ID_TRANSACCION = -1;
                conn.Close();
            }
            finally
            {
                conn.Close();
            }

            return Bitacora;
        }
        public Estructuras.Bitacora_wsTransacLinea Insert_DetalleBitacora(Estructuras.Bitacora_wsTransacLinea Bitacora)
        {

            if (Bitacora.ID_TRANSACCION > 0)
            {
                ConexionBD conexion = new ConexionBD();
                MySqlConnection conn = new MySqlConnection();
                try
                {
                    MySqlCommand cmd = new MySqlCommand();
                    conn = conexion.abrirConexionMysqlLog();
                    cmd.Connection = conn;
                    cmd.CommandText = "sp_Insert_DetalleBitacora";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add("?p_id_transaccion", MySqlDbType.UInt64).Value = Bitacora.ID_TRANSACCION;
                    cmd.Parameters.Add("?p_metodo", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.METODO;
                    cmd.Parameters.Add("?p_xml_in", MySqlDbType.LongBlob).Value = (Bitacora.Detalle_Bitacora.XML_IN != null ? Encoding.ASCII.GetBytes(Bitacora.Detalle_Bitacora.XML_IN) : null);
                    cmd.Parameters.Add("?p_mensaje", MySqlDbType.Text).Value = Bitacora.Detalle_Bitacora.MENSAJE;
                    cmd.Parameters.Add("?p_procedimiento", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.PROCEDURE;
                    cmd.Parameters.Add("?p_error", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.ERROR;
                    cmd.Parameters.Add("?p_cobro_tarjeta", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.COBRO_TARJETA;
                    cmd.Parameters.Add("?p_pagar_recibo", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.PAGAR_RECIBO;
                    cmd.Parameters.Add("?p_xml_out", MySqlDbType.LongBlob).Value = (Bitacora.Detalle_Bitacora.XML_OUT != null ? Encoding.ASCII.GetBytes(Bitacora.Detalle_Bitacora.XML_OUT) : null);
                    cmd.Parameters.Add("?p_reintegro_tarjeta", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.REINTEGRO_TARJETA;
                    cmd.Parameters.Add("?p_fecha_in", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.FECHA_IN;
                    cmd.Parameters.Add("?p_fecha_out", MySqlDbType.VarChar).Value = Bitacora.Detalle_Bitacora.FECHA_OUT;
                    cmd.Parameters.Add("?p_extra_data", MySqlDbType.LongBlob).Value = Bitacora.Detalle_Bitacora.EXTRA_DATA;
                    cmd.ExecuteNonQuery();
                    conn.Close();

                }
                catch (Exception e)
                {
                    Logger.LogErrorMessage(e, "Insert_DetalleBitacora");
                    conn.Close();
                }
                finally
                {
                    conn.Close();
                }
            }
            return Bitacora;
        }

        /// <summary>
        /// Proceso para Actualizar la informacion de la bitacora
        /// </summary>
        /// <param name="pBitacora"></param>
        /// <returns></returns>
        public Estructuras.Bitacora_wsTransacLinea Update_Bitacora(Estructuras.Bitacora_wsTransacLinea pBitacora,
                                                                   List<xmlTransaccion.requerimiento> pRequerimientos = null,
                                                                   List<xmlTransaccion.medio> pMedios = null)
        {
            if (pBitacora != null)
            {
                if (pBitacora.ID_TRANSACCION > 0)
                {
                    ConexionBD conexion = new ConexionBD();

                    #region Encabezado
                    using (MySqlConnection conn = conexion.abrirConexionMysqlLog())
                    {
                        try
                        {
                            MySqlCommand cmd = new MySqlCommand();
                            cmd.Connection = conn;
                            cmd.CommandText = "sp_Update_BitacoraWSTransacLinea";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add("?p_id_transaccion", MySqlDbType.VarChar).Value = pBitacora.ID_TRANSACCION;
                            cmd.Parameters.Add("?p_host", MySqlDbType.VarChar).Value = pBitacora.HOST;
                            cmd.Parameters.Add("?p_url", MySqlDbType.VarChar).Value = pBitacora.URL;
                            cmd.Parameters.Add("?p_mensaje", MySqlDbType.Text).Value = pBitacora.MENSAJE;
                            cmd.Parameters.Add("?p_xml_in", MySqlDbType.LongBlob).Value = (pBitacora.XML_IN != null ? Encoding.ASCII.GetBytes(pBitacora.XML_IN) : null);
                            cmd.Parameters.Add("?p_metodo", MySqlDbType.VarChar).Value = pBitacora.METODO;
                            cmd.Parameters.Add("?p_sistema", MySqlDbType.VarChar).Value = pBitacora.SISTEMA;
                            cmd.Parameters.Add("?p_usuario_origen", MySqlDbType.VarChar).Value = pBitacora.USUARIO_ORIGEN;
                            cmd.Parameters.Add("?p_codigo_usuario", MySqlDbType.VarChar).Value = pBitacora.CODIGO_USUARIO;
                            cmd.Parameters.Add("?p_error", MySqlDbType.VarChar).Value = pBitacora.ERROR;
                            cmd.Parameters.Add("?p_xml_out", MySqlDbType.LongBlob).Value = (pBitacora.XML_OUT != null ? Encoding.ASCII.GetBytes(pBitacora.XML_OUT) : null);
                            cmd.Parameters.Add("?p_id_referencia", MySqlDbType.VarChar).Value = pBitacora.ID_REFERENCIA;
                            cmd.Parameters.Add("?p_sistema_uso", MySqlDbType.VarChar).Value = pBitacora.SISTEMA_USO;
                            cmd.Parameters.Add("?p_email_notifica", MySqlDbType.VarChar).Value = pBitacora.EMAIL_NOTIFICA;
                            cmd.Parameters.Add("?p_moneda", MySqlDbType.VarChar).Value = pBitacora.MONEDA;
                            cmd.Parameters.Add("?p_total", MySqlDbType.VarChar).Value = pBitacora.TOTAL;
                            cmd.ExecuteNonQuery();
                            conn.Close();
                        }
                        catch (Exception e)
                        {
                            conn.Close();
                        }
                        finally
                        {
                            conn.Close();
                        }
                    }
                    #endregion

                    #region Requerimientos
                    if (pRequerimientos != null)
                    {
                        using (MySqlConnection conn = conexion.abrirConexionMysqlLog())
                        {
                            try
                            {
                                for (int i = 0; i < pRequerimientos.Count; i++)
                                {
                                    MySqlCommand cmd = new MySqlCommand();
                                    cmd.Connection = conn;
                                    cmd.CommandText = "fnc_Insert_Requerimientos";
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.Add("?p_id_transaccion", MySqlDbType.VarChar).Value = pBitacora.ID_TRANSACCION;
                                    cmd.Parameters.Add("?p_poliza", MySqlDbType.VarChar).Value = pRequerimientos[i].poliza;
                                    cmd.Parameters.Add("?p_requerimiento", MySqlDbType.VarChar).Value = pRequerimientos[i].numeroRequerimiento;
                                    cmd.Parameters.Add("?p_moneda", MySqlDbType.VarChar).Value = pRequerimientos[i].moneda;
                                    cmd.Parameters.Add("?p_monto", MySqlDbType.VarChar).Value = pRequerimientos[i].totalRequerimiento;
                                    cmd.Parameters.Add("?p_estado", MySqlDbType.VarChar).Value = (!string.IsNullOrWhiteSpace(pRequerimientos[i].estado) ?
                                                                                                 pRequerimientos[i].estado :
                                                                                                 "1");
                                    cmd.Parameters.Add("?respuesta", MySqlDbType.Int64).Direction = ParameterDirection.ReturnValue;
                                    cmd.ExecuteNonQuery();

                                    cmd.Dispose();
                                }
                            }
                            catch (Exception e)
                            {
                            }
                            finally
                            {
                                conn.Close();
                            }
                        }
                    }
                    #endregion

                    #region Medios de pago
                    if (pMedios != null)
                    {
                        using (MySqlConnection conn = conexion.abrirConexionMysqlLog())
                        {
                            try
                            {
                                for (int i = 0; i < pMedios.Count; i++)
                                {
                                    MySqlCommand cmd = new MySqlCommand();
                                    cmd.Connection = conn;
                                    cmd.CommandText = "fnc_Insert_Medio_Pago";
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.Add("?p_id_transaccion", MySqlDbType.VarChar).Value = pBitacora.ID_TRANSACCION;
                                    cmd.Parameters.Add("?p_tipo", MySqlDbType.VarChar).Value = pMedios[i].tipoPago;
                                    cmd.Parameters.Add("?p_entidad_financiera", MySqlDbType.VarChar).Value = pMedios[i].codEntidadFinanciera;
                                    cmd.Parameters.Add("?p_entidad_financiera_desc", MySqlDbType.VarChar).Value = pMedios[i].descEntidadFinanciera;
                                    cmd.Parameters.Add("?p_numero_documento", MySqlDbType.VarChar).Value = pMedios[i].numeroDocumento;
                                    cmd.Parameters.Add("?p_entidad_tarjeta", MySqlDbType.VarChar).Value = pMedios[i].codigoTarjeta;
                                    cmd.Parameters.Add("?p_fecha_deposito", MySqlDbType.VarChar).Value = pMedios[i].fechaDeposito;
                                    cmd.Parameters.Add("?p_fecha_vencimiento", MySqlDbType.VarChar).Value = pMedios[i].fecha_cheque;
                                    cmd.Parameters.Add("?p_no_autorizacion", MySqlDbType.Text).Value = pMedios[i].numeroAutorizacion;
                                    cmd.Parameters.Add("?p_no_referencia", MySqlDbType.VarChar).Value = pMedios[i].numeroReferencia;
                                    cmd.Parameters.Add("?p_moneda", MySqlDbType.VarChar).Value = pMedios[i].codMoneda;
                                    cmd.Parameters.Add("?p_monto", MySqlDbType.VarChar).Value = pMedios[i].monto;
                                    cmd.Parameters.Add("?p_estado", MySqlDbType.VarChar).Value = "1";
                                    cmd.Parameters.Add("?respuesta", MySqlDbType.Int64).Direction = ParameterDirection.ReturnValue;
                                    cmd.ExecuteNonQuery();
                                    cmd.Dispose();
                                }
                            }
                            catch (Exception e)
                            {
                            }
                            finally
                            {
                                conn.Close();
                            }
                        }
                    }
                    #endregion
                }
            }
            return pBitacora;
        }
        
    }
}
