using System;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Cliente API para realizar llamadas a los servicios comunes
    /// </summary>
    public class NwtCmnApiClient : BaseApiClient
    {
        /// <summary>
        /// Constructor que inicializa el cliente HTTP con la configuración del web.config
        /// </summary>
        public NwtCmnApiClient() : base("ReefApi_Cmn_BaseUrl", "ReefApi_Cmn_Username", "ReefApi_Cmn_Password")
        {
        }

        /// <summary>
        /// Consulta el listado de monedas
        /// </summary>
        /// <returns>Información del recibo en formato JSON</returns>
        public async Task<string> GetCurrenciesAsync()
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string languageValue = Constants.GetLanguage();
                string userValue = Constants.GetUser();

                string url = $"newtron/api/common/Currencies?cmpVal={companyValue}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.Accept.Clear();
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar monedas: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }
    }
}
