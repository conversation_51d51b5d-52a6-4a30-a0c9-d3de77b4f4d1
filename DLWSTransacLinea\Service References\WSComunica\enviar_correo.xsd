<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfTupleOfstringstringbase64Binary">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TupleOfstringstringbase64Binary" nillable="true" type="tns:TupleOfstringstringbase64Binary" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTupleOfstringstringbase64Binary" nillable="true" type="tns:ArrayOfTupleOfstringstringbase64Binary" />
  <xs:complexType name="TupleOfstringstringbase64Binary">
    <xs:annotation>
      <xs:appinfo>
        <GenericType Name="TupleOf{0}{1}{2}{#}" Namespace="http://schemas.datacontract.org/2004/07/System" xmlns="http://schemas.microsoft.com/2003/10/Serialization/">
          <GenericParameter Name="string" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="string" Namespace="http://www.w3.org/2001/XMLSchema" />
          <GenericParameter Name="base64Binary" Namespace="http://www.w3.org/2001/XMLSchema" />
        </GenericType>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="m_Item1" nillable="true" type="xs:string" />
      <xs:element name="m_Item2" nillable="true" type="xs:string" />
      <xs:element name="m_Item3" nillable="true" type="xs:base64Binary" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TupleOfstringstringbase64Binary" nillable="true" type="tns:TupleOfstringstringbase64Binary" />
</xs:schema>