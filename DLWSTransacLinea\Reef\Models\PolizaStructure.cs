﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    public class PolizaStructure
    {
        public OPlyGniP oPlyGniP { get; set; }
        public List<object> oPlyCciPT { get; set; }
        public List<OPlyInaPT> oPlyInaPT { get; set; }
        public List<object> oPlyIucPT { get; set; }
        public List<OPlyInePT> oPlyInePT { get; set; }
        public List<OPlyAtcCT> oPlyAtcCT { get; set; }
        public List<OPlyRkcCT> oPlyRkcCT { get; set; }
        public List<object> oPlyC1cCT { get; set; }
        public List<object> oPlyAnxPT { get; set; }
        public List<OPlyUtcPT> oPlyUtcPT { get; set; }
        public List<object> oRcpPmrPT { get; set; }
        public List<object> oPlyCvcCT { get; set; }

        public PolizaStructure()
        {
            oPlyGniP = new OPlyGniP();
            oPlyInaPT = new List<OPlyInaPT>();
            oPlyInePT = new List<OPlyInePT>();
            oPlyAtcCT = new List<OPlyAtcCT>();
            oPlyRkcCT = new List<OPlyRkcCT>();
            oPlyC1cCT = new List<object>();
            oPlyAnxPT = new List<object>();
        }
    }

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class OPlyAtcC
    {
        public OPlyAtrP oPlyAtrP { get; set; }
        public List<object> oPlyOcaPT { get; set; }
    }

    public class OPlyAtcCT
    {
        public OPlyAtcC oPlyAtcC { get; set; }
    }

    public class OPlyAtrP
    {
        public OPlyAtrS oPlyAtrS { get; set; }
    }

    public class OPlyAtrS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int rskVal { get; set; }
        public int pedVal { get; set; }
        public int lobVal { get; set; }
        public string lvlTypVal { get; set; }
        public string fldNam { get; set; }
        public string fldValVal { get; set; }
        public string fldShrVal { get; set; }
        public int sqnVal { get; set; }
        public string rskRmv { get; set; }
        public string opvRow { get; set; }
        public string aplOpvRow { get; set; }
        public string fldTxtVal { get; set; }
    }

    public class OPlyBrwPT
    {
        public OPlyBrwS oPlyBrwS { get; set; }
    }

    public class OPlyBrwS
    {
        public int cmpVal { get; set; }
        public int lobVal { get; set; }
        public string qtnVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int rskVal { get; set; }
        public int pedVal { get; set; }
        public int cvrVal { get; set; }
        public int ecmBrwCncVal { get; set; }
        public int eccVal { get; set; }
        public double anlAmn { get; set; }
        public double anlAgrAmn { get; set; }
        public double enrAmn { get; set; }
        public long nonCnmAmn { get; set; }
    }

    public class OPlyCvcC
    {
        public OPlyCvrP oPlyCvrP { get; set; }
        public List<object> oPlyAvcCT { get; set; }
        public List<OPlyBrwPT> oPlyBrwPT { get; set; }
        public List<OPlyAtcCT> oPlyAtcCT { get; set; }
    }

    public class OPlyCvcCT
    {
        public OPlyCvcC oPlyCvcC { get; set; }
    }

    public class OPlyCvrP
    {
        public OPlyCvrS oPlyCvrS { get; set; }
    }

    public class OPlyCvrS
    {
        public int cmpVal { get; set; }
        public int lobVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int rskVal { get; set; }
        public int pedVal { get; set; }
        public int sqnVal { get; set; }
        public int cvrVal { get; set; }
        public string cvrNam { get; set; }
        public int cplCrnVal { get; set; }
        public string sdrCrnVal { get; set; }
        public int untAmn { get; set; }
        public int lssDcsCplAmn { get; set; }
        public int lssDcsAcrCplAmn { get; set; }
        public long cplAmn { get; set; }
        public long enrCplAmn { get; set; }
        public int rnsSciVal { get; set; }
        public double cvrRatVal { get; set; }
        public int agcAmn { get; set; }
        public int enrAgcAmn { get; set; }
        public int enrRltAgcAmn { get; set; }
        public string cvrRmv { get; set; }
        public string rskRmv { get; set; }
        public string opvRow { get; set; }
        public string aplOpvRow { get; set; }
        public int? rltCvrCplPer { get; set; }
    }

    public class OPlyGniP
    {
        public OPlyGniS oPlyGniS { get; set; }
    }

    public class OPlyGniS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public string orgQtnVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int secVal { get; set; }
        public string secNam { get; set; }
        public int lobVal { get; set; }
        public string lobNam { get; set; }
        public long vldDat { get; set; }
        public long isuDat { get; set; }
        public long enrIsuDat { get; set; }
        public long plyEfcDat { get; set; }
        public long plyExpDat { get; set; }
        public long enrEfcDat { get; set; }
        public long enrExpDat { get; set; }
        public string plyDrtVal { get; set; }
        public string plyDrtNam { get; set; }
        public int totRskVal { get; set; }
        public int crnVal { get; set; }
        public string sdrCrnVal { get; set; }
        public int pmsVal { get; set; }
        public string pmsNam { get; set; }
        public int mxmRnwQntVal { get; set; }
        public int rnwVal { get; set; }
        public string cinTypVal { get; set; }
        public string cinTypNam { get; set; }
        public string prrPlyVal { get; set; }
        public string delVal { get; set; }
        public string gppVal { get; set; }
        public string gppSqnVal { get; set; }

        public string enrCasNam { get; set; }
        public string enrTypVal { get; set; }
        public string enrTypNam { get; set; }
        public string plyRgl { get; set; }
        public string rglTypVal { get; set; }
        public string rglTypNam { get; set; }
        public int prePymDrtVal { get; set; }
        public string mnlRnsDst { get; set; }
        public string ptaPly { get; set; }
        public string mnlPre { get; set; }
        public string prvMvm { get; set; }
        public object atzDat { get; set; }
        public string canPly { get; set; }
        public string canEnr { get; set; }
        public string tmpEnr { get; set; }
        public string mnmDaaPly { get; set; }
        public string plyPnt { get; set; }
        public string exvVal { get; set; }
        public string usrVal { get; set; }
        public long mdfDat { get; set; }
        public int cptThrLvlVal { get; set; }
        public string cptThrLvlNam { get; set; }
        public string frpRns { get; set; }
        public string tnrPlyTypVal { get; set; }
        public string tnrPlyTypNam { get; set; }
        public string rnsTypVal { get; set; }
        public string rnsTypNam { get; set; }
        public int pblEnrSqn { get; set; }
        public string intVldVal { get; set; }
        public string stm { get; set; }
        public double exrVal { get; set; }
        public string cmpNam { get; set; }
        public int? enrVal { get; set; }
        public int? enrSbdVal { get; set; }
        public string enrNam { get; set; }
        public int? cacEnrSqn { get; set; }
        public long? enrCanDat { get; set; }
    }

    public class OPlyInaPT
    {
        public OPlyInaS oPlyInaS { get; set; }
    }

    public class OPlyInaS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public string itcVal { get; set; }
        public string itcNam { get; set; }
        public int thpVal { get; set; }
        public string thpCpeNam { get; set; }
        public int parPer { get; set; }
        public int cmcVal { get; set; }
        public string cmcNam { get; set; }
        public int frsLvlVal { get; set; }
        public string frsLvlNam { get; set; }
        public int scnLvlVal { get; set; }
        public string scnLvlNam { get; set; }
        public int thrLvlVal { get; set; }
        public string thrLvlNam { get; set; }
        public string frsDstHnlVal { get; set; }
        public string frsDstHnlNam { get; set; }
        public string scnDstHnlVal { get; set; }
        public string scnDstHnlNam { get; set; }
        public string thrDstHnlVal { get; set; }
        public string thrDstHnlNam { get; set; }
    }

    public class OPlyInePT
    {
        public OPlyIneS oPlyIneS { get; set; }
    }

    public class OPlyIneS
    {
        public string thpDcmTypNam { get; set; }
        public string thpDcmTypVal { get; set; }
        public string mnrVal { get; set; }
        public string mnrTypVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int aplVal { get; set; }
        public int enrSqn { get; set; }
        public string thpDcmVal { get; set; }
        public string plyVal { get; set; }
        public int cmpVal { get; set; }
        public string bnfTypVal { get; set; }
        public int rskVal { get; set; }
        public string ctmTypNam { get; set; }
        public string bnfTypNam { get; set; }
        public string cpeNam { get; set; }
        public string mnrNam { get; set; }
        public int? parPer { get; set; }
        public string opvRow { get; set; }
        public string rowRmv { get; set; }
        public string thpRskCll { get; set; }
        public string plyRskManThp { get; set; }
        public int? sqnVal { get; set; }
        public string prrThpDcmVal { get; set; }
        public string vipVal { get; set; }
    }

    public class OPlyRkcC
    {
        public OPlyRskP oPlyRskP { get; set; }
        public List<object> oPlyPedPT { get; set; }
        public List<OPlyInePT> oPlyInePT { get; set; }
        public List<OPlyAtcCT> oPlyAtcCT { get; set; }
        public List<object> oPlyAcrPT { get; set; }
        public List<OPlyUtcPT> oPlyUtcPT { get; set; }
        public List<OPlyCvcCT> oPlyCvcCT { get; set; }
        public List<object> oPlyMilPT { get; set; }
    }

    public class OPlyUtcS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int rskVal { get; set; }
        public int cvrVal { get; set; }
        public string cvrNam { get; set; }
        public int rnsSciVal { get; set; }
        public string sysVal { get; set; }
        public string tccJmpLvlVal { get; set; }
        public string jmpLvlNam { get; set; }
        public int errVal { get; set; }
        public string errNam { get; set; }
        public int atzLvlVal { get; set; }
        public string atzLvlNam { get; set; }
        public string rjcTypVal { get; set; }
        public string rjcTypNam { get; set; }
        public string atzSysVal { get; set; }
        public string prcAreNam { get; set; }
        public string errObsVal { get; set; }
        public string tccAtz { get; set; }
        public long atzDat { get; set; }
        public string athUsrVal { get; set; }
        public string atzObsVal { get; set; }
    }

    public class OPlyUtcPT
    {
        public OPlyUtcS oPlyUtcS { get; set; }
    }

    public class OPlyRkcCT
    {
        public OPlyRkcC oPlyRkcC { get; set; }
    }

    public class OPlyRskP
    {
        public OPlyRskS oPlyRskS { get; set; }
    }

    public class OPlyRskS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int rskVal { get; set; }
        public string rskNam { get; set; }
        public string enrTypVal { get; set; }
        public int mdtVal { get; set; }
        public long rskEfcDat { get; set; }
        public long rskExpDat { get; set; }
        public string rskRmv { get; set; }
        public string opvRow { get; set; }
        public string mdtNam { get; set; }
    }

    // Clases para manejar la respuesta del endpoint queryEndorsements
    public class EndorsementResponse
    {
        public List<Endorsement> Endorsements { get; set; }

        public EndorsementResponse()
        {
            Endorsements = new List<Endorsement>();
        }
    }

    public class Endorsement
    {
        [JsonProperty("enrSqn")]
        public int EnrSqn { get; set; }

        [JsonProperty("enrTypVal")]
        public string EnrTypVal { get; set; }

        [JsonProperty("enrTypNam")]
        public string EnrTypNam { get; set; }

        [JsonProperty("enrEfcDat")]
        public long EnrEfcDat { get; set; }

        [JsonProperty("enrExpDat")]
        public long EnrExpDat { get; set; }

        [JsonProperty("enrIsuDat")]
        public long EnrIsuDat { get; set; }

        [JsonProperty("enrCasNam")]
        public string EnrCasNam { get; set; }
    }

    // Estructura para póliza grupo
    public class PolizaGrupoStructure
    {
        [JsonProperty("cmpVal")]
        public int CmpVal { get; set; }

        [JsonProperty("gppVal")]
        public string GppVal { get; set; }

        [JsonProperty("gppEnrSqn")]
        public int GppEnrSqn { get; set; }

        [JsonProperty("gppEnrTypVal")]
        public string GppEnrTypVal { get; set; }

        [JsonProperty("gppEnrSqnPbl")]
        public int GppEnrSqnPbl { get; set; }

        [JsonProperty("plyEfcDat")]
        public long PlyEfcDat { get; set; }

        [JsonProperty("plyExpDat")]
        public long PlyExpDat { get; set; }

        [JsonProperty("enrEfcDat")]
        public long EnrEfcDat { get; set; }

        [JsonProperty("enrExpDat")]
        public long EnrExpDat { get; set; }

        [JsonProperty("enrIsuDat")]
        public long EnrIsuDat { get; set; }

        [JsonProperty("usrVal")]
        public string UsrVal { get; set; }

        [JsonProperty("mdfDat")]
        public long MdfDat { get; set; }
    }

    // Estructura para deal de póliza grupo
    public class DealPolizaGrupoStructure
    {
        [JsonProperty("cmpVal")]
        public int CmpVal { get; set; }

        [JsonProperty("delVal")]
        public int DelVal { get; set; }

        [JsonProperty("delNam")]
        public string DelNam { get; set; }

        [JsonProperty("gppVal")]
        public string GppVal { get; set; }

        [JsonProperty("plyTypVal")]
        public string PlyTypVal { get; set; }

        [JsonProperty("grpMulRsk")]
        public string GrpMulRsk { get; set; }
    }
}
