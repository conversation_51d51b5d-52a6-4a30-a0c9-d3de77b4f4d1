﻿using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Structures;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Comunes
{
    public class ValidacionModule
    {
        /// <summary>
        /// Validación de documentos
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <returns></returns>
        public XElement ValidarDocumento(xmlTransaccion._transaccion xmlSolicitud)
        {
            XElement resultado = null;
            xmlTransaccion clRespuesta = new xmlTransaccion();

            DLWSTransacLinea.Cores.MySQLLogger Oper = new Cores.MySQLLogger();

            #region INIZIALIZACION
            xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
            clTransaccion.datos = new xmlTransaccion._estructuraTransaccion();
            clTransaccion.datos.encabezado = new xmlTransaccion._encabezado();
            clTransaccion.datos.polizas = new xmlTransaccion._poliza();
            clTransaccion.datos.polizas.poliza = new List<xmlTransaccion.poliza>();
            clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
            clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();
            clTransaccion.datos.identificadores = new xmlTransaccion._identificadores();
            clTransaccion.datos.mediosPago = new xmlTransaccion._medio();
            clTransaccion.datos.mediosPago.medio = new List<xmlTransaccion.medio>();
            #endregion INIZIALIZACION

            clTransaccion.datos.encabezado = xmlSolicitud.datos.encabezado;
            clTransaccion.datos.identificadores = xmlSolicitud.datos.identificadores;

            Cores.AcselDB loggerA = new Cores.AcselDB();

            switch (xmlSolicitud.datos.encabezado.tipoBusqueda)
            {
                case "LIQUIDACION":
                    if (!string.IsNullOrWhiteSpace(xmlSolicitud.datos.identificadores.num_sini))
                    {
                        Cores.TronwebDB tronwebDB = new Cores.TronwebDB();
                        DeducibleReef deducibleReef = new DeducibleReef();

                        string criterio_siniestro = string.Empty;

                        switch (xmlSolicitud.datos.identificadores.sistema)
                        {
                            case "T":
                                criterio_siniestro = tronwebDB.Core_TW_ValidarLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini);
                                break;
                            case "R":
                                criterio_siniestro = deducibleReef.ValidarLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini);
                                break;
                        }

                        if (!string.IsNullOrWhiteSpace(criterio_siniestro))
                        {
                            string[] criterioSiniestroParts = criterio_siniestro.Split('-');

                            if (criterioSiniestroParts.Length >= 3)
                            {
                                List<string> elementos = new List<string>(criterioSiniestroParts);

                                clTransaccion.datos.mensaje.tipo = elementos[0];
                                clTransaccion.datos.mensaje.mensaje = elementos[1];
                                clTransaccion.datos.mensaje.codigo = elementos[2];

                                clTransaccion.datos.encabezado.codigoRetorno = "00";
                                clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                            }
                            else
                            {
                                clTransaccion.datos.encabezado.codigoRetorno = "01";
                                clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                            }
                        }
                        else
                        {
                            clTransaccion.datos.encabezado.codigoRetorno = "01";
                            clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                        }
                    }
                    else
                    {
                        clTransaccion.datos.encabezado.codigoRetorno = "01";
                        clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                    }
                    break;
                default:
                    break;
            }

            resultado = clRespuesta.generarXmlTransaccion(clTransaccion);

            return resultado;
        }

    }
}
