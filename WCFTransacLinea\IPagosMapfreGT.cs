﻿using DLWSTransacLinea.Comunes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using System.Xml.Linq;

namespace WCFTransacLinea
{
    [ServiceContract]
    public interface IPagosMapfreGT
    {
        [OperationContract]
        string deserealizar<PERSON>a<PERSON><PERSON>(string origenXml, string raiz, string[] origenListas);

        [OperationContract]
        XElement ejecutarTransaccion(string xmlDatos);

        [OperationContract]
        string generarXmlTransaccion(_transaccion _datos);

        [OperationContract]
        string obtenerDocumentoLiq(string pNumLiquidacion);

        [OperationContract]
        string sistemaRequerimiento(string requerimiento);
    }

    [DataContract]
    public class _encabezado
    {
        #region Public Properties

        [DataMember]
        public string autorizacionBanco { get; set; }

        [DataMember]
        public string autorizacionProveedor { get; set; }

        [DataMember]
        public string codigoCajero { get; set; }

        [DataMember]
        public string codigoRetorno { get; set; }

        [DataMember]
        public string convenio { get; set; }

        [DataMember]
        public string idReferencia { get; set; }

        [DataMember]
        public string idTransaccion { get; set; }

        [DataMember]
        public string mensajeRetorno { get; set; }

        [DataMember]
        public string proveedor { get; set; }

        [DataMember]
        public string sistemaUso { get; set; }

        [DataMember]
        public string tipoBusqueda { get; set; }

        [DataMember]
        public string tipoTransaccion { get; set; }

        [DataMember]
        public string usuarioOrigen { get; set; }

        [DataMember]
        public string esAviso { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _estructuraTransaccion
    {
        #region Public Properties

        [DataMember]
        public _encabezado encabezado { get; set; }

        [DataMember]
        public _identificadores identificadores { get; set; }

        [DataMember]
        public _medio mediosPago { get; set; }

        [DataMember]
        public _poliza polizas { get; set; }

        [DataMember]
        public _requerimiento requerimientos { get; set; }

        [DataMember]
        public _liquidacion liquidaciones { get; set; }
        [DataMember]
        public _requerimientos resumenPago { get; set; }

        [DataMember]
        public _mensaje mensaje { get; set; }

        #endregion Public Properties

        public _estructuraTransaccion()
        {
            mensaje = new _mensaje();
        }
    }

    [DataContract]
    public class _mensaje
    {
        [DataMember]
        public string tipo { get; set; }
        [DataMember]
        public string titulo { get; set; }
        [DataMember]
        public string mensaje { get; set; }
        [DataMember]
        public string codigo { get; set; }
    }

    [DataContract]
    public class _identificadores
    {
        #region Public Properties

        [DataMember]
        public string codpol { get; set; }

        [DataMember]
        public string dvid { get; set; }

        [DataMember]
        public string idepol { get; set; }

        [DataMember]
        public string nit { get; set; }

        [DataMember]
        public string numcert { get; set; }

        [DataMember]
        public string numid { get; set; }

        [DataMember]
        public string numpol { get; set; }

        [DataMember]
        public string requerimiento { get; set; }

        [DataMember]
        public string sistema { get; set; }

        [DataMember]
        public string vigencia_inicial { get; set; }

        [DataMember]
        public string es_poliza_grupo { get; set; }
        [DataMember]
        public string tip_docum { get; set; }
        [DataMember]
        public string cod_docum { get; set; }
        [DataMember]
        public string num_sini { get; set; }
        [DataMember]
        public string num_cuotas { get; set; }
        [DataMember]
        public string mca_factura { get; set; }
        #endregion Public Properties
    }

    [DataContract]
    public class _medio
    {
        #region Public Properties

        [DataMember]
        public List<medio> medio { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _poliza
    {
        #region Public Properties

        [DataMember]
        public List<poliza> poliza { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _requerimiento
    {
        #region Public Properties

        [DataMember]
        public List<requerimiento> requerimiento { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _liquidacion
    {
        #region Public Properties

        [DataMember]
        public List<liquidacion> liquidacion { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _requerimientos
    {
        #region Public Properties

        //ahora si
        [DataMember]
        public _requerimiento requerimientos { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class _transaccion
    {
        #region Public Properties

        [DataMember]
        public _estructuraTransaccion datos { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class medio
    {
        #region Public Properties

        [DataMember]
        public string codEntidadFinanciera { get; set; }

        [DataMember]
        public string codigoTarjeta { get; set; }

        [DataMember]
        public string codMoneda { get; set; }

        [DataMember]
        public string CUENTA_SIMPLIFICADA { get; set; }

        [DataMember]
        public string descEntidadFinanciera { get; set; }

        [DataMember]
        public string DESCRIPCION { get; set; }

        [DataMember]
        public string fecha_cheque { get; set; }

        [DataMember]
        public string fechaDeposito { get; set; }

        [DataMember]
        public string monto { get; set; }

        [DataMember]
        public string numeroAutorizacion { get; set; }

        [DataMember]
        public string numeroDocumento { get; set; }

        [DataMember]
        public string numeroReferencia { get; set; }

        [DataMember]
        public string tipoPago { get; set; }

        [DataMember]
        public string tipoTarjeta { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class poliza
    {
        #region Public Properties

        [DataMember]
        public string asegurado { get; set; }

        [DataMember]
        public string certificado { get; set; }

        [DataMember]
        public string codFact { get; set; }

        [DataMember]
        public string estadoPoliza { get; set; }

        [DataMember]
        public string finVigencia { get; set; }

        [DataMember]
        public string idepol { get; set; }

        [DataMember]
        public string inicioVigencia { get; set; }

        [DataMember]
        public string mca_provisional { get; set; }

        [DataMember]
        public string numPoliza { get; set; }

        [DataMember]
        public string sistema { get; set; }

        [DataMember]
        public string poliza_grupo { get; set; }

        [DataMember]
        public string num_contrato { get; set; }

        [DataMember]
        public string tip_docum { get; set; }

        [DataMember]
        public string cod_docum { get; set; }
        #endregion Public Properties
    }

    [DataContract]
    public class requerimiento
    {
        #region Public Properties

        [DataMember]
        public string asignaFactura { get; set; }

        [DataMember]
        public string cae { get; set; }

        [DataMember]
        public string codFact { get; set; }

        [DataMember]
        public string direc_cobro { get; set; }

        [DataMember]
        public string email { get; set; }

        [DataMember]
        public string estado { get; set; }

        [DataMember]
        public string fechaCobroRequerimiento { get; set; }

        [DataMember]
        public string idepol { get; set; }

        [DataMember]
        public string id_moneda { get; set; }

        [DataMember]
        public string moneda { get; set; }

        [DataMember]
        public string nitPagador { get; set; }

        [DataMember]
        public string tip_docum { get; set; }

        [DataMember]
        public string cod_docum { get; set; }

        [DataMember]
        public string nombrePagador { get; set; }

        [DataMember]
        public string numeroCuota { get; set; }

        [DataMember]
        public string numeroRequerimiento { get; set; }

        [DataMember]
        public string numFact { get; set; }

        [DataMember]
        public string poliza { get; set; }

        [DataMember]
        public string reling { get; set; }

        [DataMember]
        public string sistema { get; set; }

        [DataMember]
        public string totalRequerimiento { get; set; }

        [DataMember]
        public string vencimientoRequerimiento { get; set; }
        [DataMember]
        public string es_aviso { get; set; }

        #endregion Public Properties
    }

    [DataContract]
    public class liquidacion
    {
        #region Public Properties
        [DataMember]
        public string id_moneda { get; set; }
        [DataMember]
        public string moneda { get; set; }
        [DataMember]
        public string mto_deducible { get; set; }
        [DataMember]
        public string mto_iva { get; set; }
        [DataMember]
        public string mto_cuotas { get; set; }
        [DataMember]
        public string mto_total { get; set; }
        [DataMember]
        public string num_deducible { get; set; }
        #endregion
    }
}