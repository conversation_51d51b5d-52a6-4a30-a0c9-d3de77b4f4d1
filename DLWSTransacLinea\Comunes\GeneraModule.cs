﻿using DLWSTransacLinea.Cores;
using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Structures;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Comunes
{
    public class GeneraModule
    {

        /// <summary>
        /// Proceso para genrear documentos
        /// </summary>
        /// <param name="xmlSolicitud"></param>
        /// <param name="Bitacora"></param>
        /// <returns></returns>
        public XElement GeneraDocumento(xmlTransaccion._transaccion xmlSolicitud, Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            XElement resultado = null;
            xmlTransaccion clRespuesta = new xmlTransaccion();

            DLWSTransacLinea.Cores.MySQLLogger Oper = new Cores.MySQLLogger();

            #region INIZIALIZACION
            xmlTransaccion._transaccion clTransaccion = new xmlTransaccion._transaccion();
            clTransaccion.datos = new xmlTransaccion._estructuraTransaccion();
            clTransaccion.datos.encabezado = new xmlTransaccion._encabezado();
            clTransaccion.datos.polizas = new xmlTransaccion._poliza();
            clTransaccion.datos.polizas.poliza = new List<xmlTransaccion.poliza>();
            clTransaccion.datos.requerimientos = new xmlTransaccion._requerimiento();
            clTransaccion.datos.requerimientos.requerimiento = new List<xmlTransaccion.requerimiento>();
            clTransaccion.datos.identificadores = new xmlTransaccion._identificadores();
            clTransaccion.datos.mediosPago = new xmlTransaccion._medio();
            clTransaccion.datos.mediosPago.medio = new List<xmlTransaccion.medio>();
            #endregion INIZIALIZACION

            #region ASIGNACION
            clTransaccion.datos.encabezado = xmlSolicitud.datos.encabezado;
            clTransaccion.datos.identificadores = xmlSolicitud.datos.identificadores;
            #endregion ASIGNACION

            Cores.AcselDB loggerA = new Cores.AcselDB();

            #region TIPO | TRANSACCION
            clTransaccion.datos.encabezado.idTransaccion = Bitacora.ID_TRANSACCION.ToString();

            switch (xmlSolicitud.datos.encabezado.tipoBusqueda)
            {
                case "LIQUIDACION":
                    if (!string.IsNullOrWhiteSpace(xmlSolicitud.datos.identificadores.num_sini))
                    {
                        DeducibleReef deducibleReef = new DeducibleReef();
                        TronwebDB tronwebDB = new TronwebDB();

                        long num_deducible = 0;

                        switch (xmlSolicitud.datos.identificadores.sistema)
                        {
                            case "T":
                                num_deducible = tronwebDB.CrearLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini,
                                                                                     p_mca_factura: xmlSolicitud.datos.identificadores.mca_factura,
                                                                                     p_num_cuota: xmlSolicitud.datos.identificadores.num_cuotas,
                                                                                     p_cod_cia: "2",
                                                                                     Bitacora: Bitacora);
                                break;
                            case "R":
                                num_deducible = deducibleReef.CrearLiquidacion(p_num_sini: xmlSolicitud.datos.identificadores.num_sini,
                                                                               p_mca_factura: xmlSolicitud.datos.identificadores.mca_factura,
                                                                               p_num_cuota: xmlSolicitud.datos.identificadores.num_cuotas,
                                                                               Bitacora: Bitacora);
                                break;
                        }

                        if (num_deducible > 0)
                        {
                            clTransaccion.datos.liquidaciones = new xmlTransaccion._liquidacion();
                            clTransaccion.datos.liquidaciones.liquidacion = new List<xmlTransaccion.liquidacion>();

                            clTransaccion.datos.liquidaciones.liquidacion.Add(new xmlTransaccion.liquidacion()
                            {
                                num_deducible = num_deducible
                            });

                            clTransaccion.datos.encabezado.codigoRetorno = "00";
                            clTransaccion.datos.encabezado.mensajeRetorno = "CONSULTA REALIZADA CON EXITO.";
                        }
                        else
                        {
                            clTransaccion.datos.encabezado.codigoRetorno = "01";
                            clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                        }
                    }
                    else
                    {
                        clTransaccion.datos.encabezado.codigoRetorno = "01";
                        clTransaccion.datos.encabezado.mensajeRetorno = "IDENTIFICADOR NO EXISTE";
                    }
                    break;
                default:
                    break;
            }
            #endregion TIPO | BUSQUEDA

            #region GENERACION | XML
            resultado = clRespuesta.generarXmlTransaccion(clTransaccion);
            #endregion GENERACION | XML

            return resultado;
        }

    }
}
