﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Api
{
    public class Constants
    {
        public class Systems
        {
            public static string ACSEL = "A";
            public static string TRONWEB = "T";
            public static string REEF = "R";
        }

        /// <summary>
        /// Recupera la compania
        /// </summary>
        /// <returns></returns>
        public static string GetCompany()
        {
            return ConfigurationManager.AppSettings["ReefApi_CompanyValue"];
        }

        /// <summary>
        /// Recupera el idioma por default
        /// </summary>
        /// <returns></returns>
        public static string GetLanguage()
        {
            return ConfigurationManager.AppSettings["ReefApi_Language"];
        }

        /// <summary>
        /// Recupera el usuario por default
        /// </summary>
        /// <returns></returns>
        public static string GetUser()
        {
            return ConfigurationManager.AppSettings["ReefApi_User"];
        }

        public static bool IgnoreSslCertificate()
        {
            try
            {
                string configValue = ConfigurationManager.AppSettings["ReefApi_ignore_Ssl"] ?? "N";
                return string.Equals(configValue, "S", StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
