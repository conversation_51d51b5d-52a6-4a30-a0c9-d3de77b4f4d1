﻿using DLWSTransacLinea.Cores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea
{
    /// <summary>
    /// Proceso de cobros POS
    /// </summary>
    public class POS : IPOS
    {
        /// <summary>
        /// Proceso de cobrar
        /// </summary>
        /// <param name="xmlPOS"></param>
        /// <returns></returns>
        public string cobrar(string xmlPOS)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            // xmlPOS = "<cobroPOS><pCodProd>PLWQ</pCodProd><pNumPol>1</pNumPol><pNumCert>1</pNumCert><pCodEntFinan>000208</pCodEntFinan><pCardNumber>****************</pCardNumber><pMonto>1</pMonto><pFecVencTDC>03/2023</pFecVencTDC><pCodUsr>ACSEL_WEB</pCodUsr></cobroPOS>";

            respuesta = logger.cobrarPOSAcsel(xmlPOS);

            return respuesta;
        }

        /// <summary>
        /// Proceso para reservar el cobro
        /// </summary>
        /// <param name="xmlPOS"></param>
        /// <param name="pIdTransaccion"></param>
        /// <returns></returns>
        public string reversar(string xmlPOS, string pIdTransaccion = null)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            // xmlPOS = "<respuestaPOS><StsPos>AUT</StsPos><TDCNumber>-4566-21**-****-05647-</TDCNumber><Monto>1</Monto><FecVencTDC>2303</FecVencTDC><CodRespuesta>00</CodRespuesta><DescRespuesta>APROBADA</DescRespuesta><FechaAuto>2021-08-05</FechaAuto><HoraAuto>105359</HoraAuto><NumReferencia>90698479</NumReferencia><NumAutorizacion>622842</NumAutorizacion><IDTransaccion>046828</IDTransaccion><IdTerminal>MAPCOB01</IdTerminal></respuestaPOS>";

            respuesta = logger.reversarPOS(xmlPOS, pIdTransaccion);

            return respuesta;
        }
    }
}
