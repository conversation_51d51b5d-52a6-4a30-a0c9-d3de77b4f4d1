<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://10.115.14.4/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras" />
  <xs:element name="doEnviar">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="p_id_correo" nillable="true" type="xs:string" />
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras" minOccurs="0" name="datos" nillable="true" type="q1:dato_correo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="doEnviarResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="doEnviarResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>