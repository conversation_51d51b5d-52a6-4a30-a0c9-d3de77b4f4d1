@echo off
echo Limpiando cache de Git para aplicar el nuevo .gitignore...

REM Añadir el archivo .gitignore modificado
git add .gitignore
git commit -m "Actualizar .gitignore para excluir carpetas bin, obj y .vs"

REM Eliminar archivos específicos del índice de Git (sin borrarlos del disco)
git rm -r --cached .vs/
git rm -r --cached DLWSTransacLinea/bin/
git rm -r --cached DLWSTransacLinea/obj/
git rm -r --cached WCFTransacLinea/bin/
git rm -r --cached WCFTransacLinea/obj/

REM Añadir todos los archivos de nuevo (respetando el nuevo .gitignore)
git add .

REM Hacer commit de los cambios
git commit -m "Aplicar nuevo .gitignore a los archivos existentes"

echo Proceso completado. Las carpetas ahora deberían ser ignoradas por Git.
pause
