﻿using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Services;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Converters
{
    public class ReciboConverter
    {
        /// <summary>
        /// Cache para almacenar información de recibos por póliza
        /// Clave: "NumPoliza", Valor: InfoReciboStructure (información del pagador de la póliza)
        /// </summary>
        private static readonly Dictionary<string, InfoReciboStructure> _cacheInfoReciboPorPoliza = new Dictionary<string, InfoReciboStructure>();

        /// <summary>
        /// Lock para acceso thread-safe al cache
        /// </summary>
        private static readonly object _cacheLock = new object();
        /// <summary>
        /// Obtiene información del recibo desde cache o API, evitando consultas redundantes para la misma póliza
        /// </summary>
        /// <param name="numRecibo">Número de recibo</param>
        /// <param name="numPoliza">Número de póliza</param>
        /// <param name="reciboService">Servicio de recibos para realizar la consulta si no está en cache</param>
        /// <returns>Información del recibo</returns>
        private InfoReciboStructure ObtenerInformacionReciboOptimizada(string numRecibo, string numPoliza, ReciboService reciboService)
        {
            try
            {
                lock (_cacheLock)
                {
                    // Verificar si ya tenemos información del pagador para esta póliza en cache
                    if (_cacheInfoReciboPorPoliza.ContainsKey(numPoliza))
                    {
                        Console.WriteLine($"Información de recibo obtenida desde cache para póliza {numPoliza}");
                        return _cacheInfoReciboPorPoliza[numPoliza];
                    }
                }

                // Si no está en cache, consultar la API
                var infoRecibo = reciboService.ConsultarInformacionRecibo(numRecibo);
                if (infoRecibo == null)
                {
                    return null;
                }

                lock (_cacheLock)
                {
                    // Almacenar la información obtenida en cache por póliza
                    if (!_cacheInfoReciboPorPoliza.ContainsKey(numPoliza))
                    {
                        _cacheInfoReciboPorPoliza[numPoliza] = infoRecibo;
                        Console.WriteLine($"Información de recibo almacenada en cache para póliza {numPoliza} - Pagador: {infoRecibo.NombreCompleto}");
                    }
                }

                return infoRecibo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener información optimizada del recibo {numRecibo}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Limpia el cache de información de recibos
        /// </summary>
        public static void LimpiarCacheInfoRecibo()
        {
            lock (_cacheLock)
            {
                _cacheInfoReciboPorPoliza.Clear();
                Console.WriteLine("Cache de información de recibos limpiado");
            }
        }

        /// <summary>
        /// Convierte la estructura ORcpErcC a DataTable con servicio de recibo reutilizable
        /// </summary>
        /// <param name="receiptInformation">Información recibo</param>
        /// <param name="currenciesList">Listado de monedas</param>
        /// <returns>DataTable con los datos del recibo</returns>
        public DataTable ConvertReciboToDataTable(ORcpErcC receiptInformation, List<MonedaStructure> currenciesList)
        {
            try
            {
                DataTable dataTable = new DataTable("Recibo");
                dataTable.Columns.Add("REQUERIMIENTO", typeof(string));
                dataTable.Columns.Add("POLIZA", typeof(string));
                dataTable.Columns.Add("ID_MONEDA", typeof(string));
                dataTable.Columns.Add("MONEDA", typeof(string));
                dataTable.Columns.Add("TOTAL", typeof(string));
                dataTable.Columns.Add("FECHA_VENCIMIENTO", typeof(string));
                dataTable.Columns.Add("PAGADOR", typeof(string));
                dataTable.Columns.Add("SISTEMA", typeof(string));
                dataTable.Columns.Add("PAGO", typeof(string));
                dataTable.Columns.Add("STSFACT", typeof(string));
                dataTable.Columns.Add("IDEPOL", typeof(string));
                dataTable.Columns.Add("FECHA_COBRO", typeof(string));
                dataTable.Columns.Add("ASIGNA_FACTURA", typeof(string));
                dataTable.Columns.Add("CODFACT", typeof(string));
                dataTable.Columns.Add("EMAIL", typeof(string));
                dataTable.Columns.Add("DIREC_COBRO", typeof(string));
                dataTable.Columns.Add("ES_AVISO", typeof(string));
                dataTable.Columns.Add("TIP_DOCUM", typeof(string));
                dataTable.Columns.Add("COD_DOCUM", typeof(string));

                if (receiptInformation != null && receiptInformation.oRcpPmiPT.Count > 0)
                {
                    var ultimoSuplementoRecibo = receiptInformation.oRcpPmiPT.FirstOrDefault();

                    if (receiptInformation.oRcpPmrP.oRcpPmrS.rcpAmn > 0)
                    {
                        var monedaInformacion = currenciesList.Where(x => x.oCrnCrnS.crnVal == receiptInformation.oRcpPmrP.oRcpPmrS.crnVal).FirstOrDefault();

                        DateTime fechaVencimientoRecibo = Utils.UnixTimeStampToDateTime(receiptInformation.oRcpPmrP.oRcpPmrS.rcpExpDat);

                        DataRow row = dataTable.NewRow();
                        row["REQUERIMIENTO"] = receiptInformation.oRcpPmrP.oRcpPmrS.rcpVal;
                        row["POLIZA"] = receiptInformation.oRcpPmrP.oRcpPmrS.plyVal;
                        row["ID_MONEDA"] = monedaInformacion.oCrnCrnS.crnVal;
                        row["MONEDA"] = monedaInformacion.oCrnCrnS.sdrCrnVal;
                        row["TOTAL"] = receiptInformation.oRcpPmrP.oRcpPmrS.rcpAmn;
                        row["FECHA_VENCIMIENTO"] = fechaVencimientoRecibo.ToString("dd/MM/yyyy");
                        row["SISTEMA"] = Constants.Systems.REEF;
                        row["PAGO"] = ultimoSuplementoRecibo.oRcpPmiS.inmVal;
                        row["STSFACT"] = receiptInformation.oRcpPmrP.oRcpPmrS.rcpStsTypVal;
                        row["IDEPOL"] = receiptInformation.oRcpPmrP.oRcpPmrS.plyVal;
                        row["ASIGNA_FACTURA"] = "S";
                        row["ES_AVISO"] = "N";

                        #region RESPONSABLE|PAGO
                        try
                        {
                            ReciboService reciboService = new ReciboService();
                            string numRecibo = receiptInformation.oRcpPmrP.oRcpPmrS.rcpVal.ToString();
                            string numPoliza = receiptInformation.oRcpPmrP.oRcpPmrS.plyVal;

                            InfoReciboStructure infoReciboStructure = reciboService.ConsultarInformacionRecibo(numRecibo: numRecibo);

                            if (infoReciboStructure != null)
                            {
                                row["PAGADOR"] = infoReciboStructure.NombreCompleto;
                                row["CODFACT"] = "PENDIENTE";//PENDIENTE
                                row["EMAIL"] = infoReciboStructure.Email;
                                row["DIREC_COBRO"] = infoReciboStructure.DirecCobro;
                                row["TIP_DOCUM"] = infoReciboStructure.TipDocum;
                                row["COD_DOCUM"] = infoReciboStructure.CodDocum;

                                if (receiptInformation.oRcpPmrP.oRcpPmrS.rcpStsTypVal == "CT")
                                {
                                    row["FECHA_COBRO"] = infoReciboStructure.FechaContable;
                                }
                            }
                            else
                            {
                                row["PAGADOR"] = "PENDIENTE";
                                row["CODFACT"] = "PENDIENTE";
                                row["EMAIL"] = "<EMAIL>";
                                row["DIREC_COBRO"] = "CIUDAD";//TEMPORAL
                                row["TIP_DOCUM"] = "NIT";//TEMPORAL
                                row["COD_DOCUM"] = "107446960";//TEMPORAL
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error al obtener información del responsable de pago: {ex.Message}");
                        }
                        #endregion

                        dataTable.Rows.Add(row);
                    }
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir ORcpErcC a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
