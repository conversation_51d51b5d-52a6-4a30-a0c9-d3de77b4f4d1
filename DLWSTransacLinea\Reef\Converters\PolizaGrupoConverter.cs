using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;

namespace DLWSTransacLinea.Reef.Converters
{
    /// <summary>
    /// Clase para convertir objetos de póliza grupo entre diferentes formatos
    /// </summary>
    public class PolizaGrupoConverter
    {
        /// <summary>
        /// Convierte una lista de PolizaGrupoStructure a un DataTable
        /// </summary>
        /// <param name="polizasGrupo"></param>
        /// <param name="dealsPolizaGrupo"></param>
        /// <returns></returns>
        public DataTable ConvertPolizaGrupoToDataTable(List<PolizaGrupoStructure> polizasGrupo, List<DealPolizaGrupoStructure> dealsPolizaGrupo)
        {
            try
            {
                DataTable dataTable = new DataTable("Poliza");

                dataTable.Columns.Add("idepol", typeof(string));
                dataTable.Columns.Add("numpoliza", typeof(string));
                dataTable.Columns.Add("num_contrato", typeof(string));
                dataTable.Columns.Add("tip_docum", typeof(string));
                dataTable.Columns.Add("cod_docum", typeof(string));
                dataTable.Columns.Add("asegurado", typeof(string));
                dataTable.Columns.Add("estadopoliza", typeof(string));
                dataTable.Columns.Add("iniciovigencia", typeof(string));
                dataTable.Columns.Add("finvigencia", typeof(string));
                dataTable.Columns.Add("mca_provisional", typeof(string));
                dataTable.Columns.Add("certificado", typeof(string));
                dataTable.Columns.Add("sistema", typeof(string));
                dataTable.Columns.Add("poliza_grupo", typeof(string));

                var infoPolizaGrupo = dealsPolizaGrupo.FirstOrDefault();

                if (polizasGrupo != null && polizasGrupo.Any())
                {
                    foreach (var polizaGrupo in polizasGrupo)
                    {
                        DataRow row = dataTable.NewRow();

                        DateTime fechaHoy = DateTime.Now;
                        DateTime fechaInicioVigencia = Utils.UnixTimeStampToDateTime(polizaGrupo.EnrEfcDat);
                        DateTime fechaFinVigencia = Utils.UnixTimeStampToDateTime(polizaGrupo.EnrExpDat);

                        row["idepol"] = polizaGrupo.GppVal;
                        row["numpoliza"] = polizaGrupo.GppVal;
                        row["certificado"] = "1";
                        row["mca_provisional"] = "N";
                        row["num_contrato"] = infoPolizaGrupo.DelVal;
                        row["tip_docum"] = null;//PENDIENTE
                        row["cod_docum"] = null;//PENDIENTE
                        row["asegurado"] = infoPolizaGrupo.DelNam;
                        row["poliza_grupo"] = "S";
                        row["iniciovigencia"] = fechaInicioVigencia.ToString("dd/MM/yyyy");
                        row["finvigencia"] = fechaFinVigencia.ToString("dd/MM/yyyy");
                        row["sistema"] = Constants.Systems.REEF;

                        if (fechaFinVigencia < fechaHoy) { row["estadopoliza"] = "VEN"; }
                        else { row["estadopoliza"] = "ACT"; }

                        dataTable.Rows.Add(row);
                    }
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir PolizaGrupoStructure a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
