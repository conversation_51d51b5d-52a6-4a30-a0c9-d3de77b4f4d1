using System;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Cliente API para realizar llamadas a los servicios de emisión
    /// </summary>
    public class NwtIsuApiClient : BaseApiClient
    {
        /// <summary>
        /// Constructor que inicializa el cliente HTTP con la configuración del web.config
        /// </summary>
        public NwtIsuApiClient() : base("ReefApi_Isu_BaseUrl", "ReefApi_Isu_Username", "ReefApi_Isu_Password")
        {
        }

        /// <summary>
        /// Consulta la información operativa de una póliza
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="aplVal">Valor de aplicación (por defecto 0)</param>
        /// <returns>Información de la póliza en formato JSON</returns>
        public async Task<string> GetPolicyOperativeDataAsync(string policyNumber, string aplVal = "0")
        {
            try
            {
                string url = $"newtron/api/issue/business_line/policy/{policyNumber}/queryOperativeData?aplVal={aplVal}";

                // Usar el nuevo método con retry automático y limpieza de caché
                return await GetStringWithRetryAsync(url);
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar la póliza: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los suplementos (endorsements) de una póliza utilizando el endpoint queryEndorsements
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <returns>Información de los suplementos en formato JSON</returns>
        public async Task<string> GetPolicySupplementsAsync(string policyNumber)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string userValue = Constants.GetUser();

                string url = $"newtron/api/issue/business_line/policy/{policyNumber}/generalinformation/queryEndorsements?cmpVal={companyValue}&usrVal={userValue}";

                var content = new StringContent("", Encoding.UTF8, "application/json");

                // Usar el nuevo método con retry automático y limpieza de caché
                return await PostStringWithRetryAsync(url, content);
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar los suplementos de la póliza: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta la información de la póliza por suplemento usando el endpoint queryByEnr
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="enrSqn">Secuencia del suplemento</param>
        /// <param name="aplEnrSqn">Secuencia de aplicación del suplemento</param>
        /// <param name="aplVal">Valor de aplicación</param>
        /// <returns>Información de la póliza en formato JSON</returns>
        public async Task<string> GetPolicyByEndorsementAsync(string policyNumber, int enrSqn, string aplEnrSqn = "0", string aplVal = "0")
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string userValue = Constants.GetUser();
                string languageValue = Constants.GetLanguage();

                string url = $"newtron/api/issue/business_line/policy/{policyNumber}/queryByEnr?aplEnrSqn={aplEnrSqn}&aplVal={aplVal}&cmpVal={companyValue}&enrSqn={enrSqn}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.Accept.Clear();
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(
                    $"Error al consultar la póliza por suplemento: {ex.Message}",
                    ex
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta la información de la póliza por suplemento usando el endpoint endorsement/operativedata
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="endorsementNumber">Número de suplemento</param>
        /// <returns>Información de la póliza según el suplemento en formato JSON</returns>
        public async Task<string> GetPolicyEndorsementOperativeDataAsync(string policyNumber, int endorsementNumber)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string userValue = Constants.GetUser();
                string languageValue = Constants.GetLanguage();

                // Construir URL para el endpoint endorsement/operativedata
                string url = $"newtron/api/issue/business_line/policy/{policyNumber}/endorsement/{endorsementNumber}/operativedata?cmpVal={companyValue}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.Accept.Clear();
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(
                    $"Error al consultar la información operativa de la póliza por suplemento: {ex.Message}",
                    ex
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los suplementos de una póliza grupo utilizando el endpoint groupPolicy/endorsement/query
        /// </summary>
        /// <param name="gppVal">Número de póliza grupo</param>
        /// <returns>Información de los suplementos de la póliza grupo en formato JSON</returns>
        public async Task<string> GetGroupPolicyEndorsementsAsync(string gppVal)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string userValue = Constants.GetUser();
                string languageValue = Constants.GetLanguage();

                string url = $"newtron/api/issue/business_line/groupPolicy/endorsement/query?cmpVal={companyValue}&gppVal={gppVal}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.Accept.Clear();
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(
                    $"Error al consultar los suplementos de la póliza grupo: {ex.Message}",
                    ex
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los deals de una póliza grupo utilizando el endpoint groupPolicy/deal/queryByPolicy
        /// </summary>
        /// <param name="gppVal">Número de póliza grupo</param>
        /// <param name="delVal">Valor del deal</param>
        /// <returns>Información de los deals de la póliza grupo en formato JSON</returns>
        public async Task<string> GetGroupPolicyDealsByPolicyAsync(string gppVal, string delVal)
        {
            try
            {
                string companyValue = Constants.GetCompany();
                string userValue = Constants.GetUser();
                string languageValue = Constants.GetLanguage();

                string url = $"newtron/api/issue/business_line/groupPolicy/deal/queryByPolicy?cmpVal={companyValue}&delVal={delVal}&gppVal={gppVal}&usrVal={userValue}";

                // Usar el nuevo método con retry automático y limpieza de caché
                using (var response = await SendRequestWithRetryAsync(() =>
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.Accept.Clear();
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    request.Headers.Add("lngVal", languageValue);
                    return request;
                }))
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(
                    $"Error al consultar los deals de la póliza grupo: {ex.Message}",
                    ex
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

    }
}
