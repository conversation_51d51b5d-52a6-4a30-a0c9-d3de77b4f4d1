﻿using DLWSTransacLinea.Comunes;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.BusinessLogic
{
    public class CobranzaReef
    {
        public DataSet cobrarReciboAviso(string xmlDetalleCobro, Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            return new DataSet();
            /*string resultado = string.Empty;
            DataSet dtsRespuesta = new DataSet();
            Estructuras clEstructuras = new Estructuras();

            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.XML_IN = xmlDetalleCobro;
            Detalle_Bitacora.METODO = "cobrarTronWebv2";
            Detalle_Bitacora.PROCEDURE = "GC_K_WS_COBRO_ONLINE.F_COBRAR_RECIBO";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            //xmlDetalleCobro = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "GC_K_WS_COBRO_ONLINE.F_COBRAR_RECIBO";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_recibos", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Clob);

                p1.Value = xmlDetalleCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                resultado = (((OracleClob)cmd.Parameters["Return_Value"].Value)).Value;

                conexion.Close();

                dtsRespuesta = clEstructuras.XML_DataSet(resultado);

                if (dtsRespuesta.Tables.Count > 0)
                {
                    if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "0")
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "1";
                        Detalle_Bitacora.ERROR = "0";
                    }
                    else
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "0";
                        Detalle_Bitacora.ERROR = "1";
                        Detalle_Bitacora.MENSAJE = dtsRespuesta.Tables[0].Rows[0]["Descripcion"].ToString();
                    }
                }
                Detalle_Bitacora.XML_OUT = resultado.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Insert_DetalleBitacora(Bitacora);
            }
            catch (OracleException ex)
            {
                conexion.Close();

                resultado = "ERROR " + ex.Message;
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return dtsRespuesta;*/
        }

        public DataSet cobrarDeducible(string xmlDetalleCobro, Estructuras.Bitacora_wsTransacLinea Bitacora)
        {
            return new DataSet();
            /*string resultado = string.Empty;
            DataSet dtsRespuesta = new DataSet();
            Estructuras clEstructuras = new Estructuras();

            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.XML_IN = xmlDetalleCobro;
            Detalle_Bitacora.METODO = "cobrarDeducibleT";
            Detalle_Bitacora.PROCEDURE = "gc_k_ws_cobro_liq_online.f_cobra_liquidacion";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            //xmlDetalleCobro = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "gc_k_ws_cobro_liq_online.f_cobra_liquidacion";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_liquidacion", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Clob);

                p1.Value = xmlDetalleCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                resultado = (((OracleClob)cmd.Parameters["Return_Value"].Value)).Value;

                conexion.Close();

                dtsRespuesta = clEstructuras.XML_DataSet(resultado);

                if (dtsRespuesta.Tables.Count > 0)
                {
                    if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "0")
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "1";
                        Detalle_Bitacora.ERROR = "0";
                    }
                    else
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "0";
                        Detalle_Bitacora.ERROR = "1";
                        Detalle_Bitacora.MENSAJE = dtsRespuesta.Tables[0].Rows[0]["Descripcion"].ToString();
                    }
                }
                Detalle_Bitacora.XML_OUT = resultado.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Insert_DetalleBitacora(Bitacora);
            }
            catch (OracleException ex)
            {
                conexion.Close();

                resultado = "ERROR " + ex.Message;
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return dtsRespuesta;*/
        }
    }
}
