﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;

namespace DLWSTransacLinea.Comunes
{
    public class Estructuras
    {
        public class requerimeinto
        {
            public string REQUERIMIENTO { get; set; }
            public string MONEDA { get; set; }
            public string TOTAL { get; set; }
            public string VENCIMIENTO { get; set; }
            public string CLIENTE { get; set; }
            public string ESTADO { get; set; }
            public string CUOTA { get; set; }
            public string SISTEMA { get; set; }
        }
        public class respuestaServicio
        {
            public string convenio { get; set; }
            public string proveedor { get; set; }
            public string codigoRetorno { get; set; }
            public string mensajeRetorno { get; set; }
            public string requerimiento { get; set; }
            public string idepol { get; set; }
            public string codpol { get; set; }
            public string numpol { get; set; }
            public string dvid { get; set; }
            public string numid { get; set; }
            public string numcert { get; set; }
            public string moneda { get; set; }
            public string total_requerimiento { get; set; }
            public string vencimiento_requerimiento { get; set; }
            public string nombre_cliente { get; set; }
            public string sistema_requerimiento { get; set; }
            public string numero_cuota { get; set; }
        }
        public class solicitudServicio
        {
            public string convenio { get; set; }
            public string proveedor { get; set; }
            public string codigoRetorno { get; set; }
            public string mensajeRetorno { get; set; }
            public string identificador1 { get; set; }
            public string identificador2 { get; set; }
            public string identificador3 { get; set; }
            public string identificador4 { get; set; }
            public string identificador5 { get; set; }
            public string identificador6 { get; set; }
            public string valor1 { get; set; }
            public string valor2 { get; set; }
            public string valor3 { get; set; }
            public string valor4 { get; set; }
            public string valor5 { get; set; }
            public string valor6 { get; set; }
        }
        public string generarXmlRespuesta(respuestaServicio datos)
        {
            var xmlfromLINQ = new XElement("Mensaje",

                new XElement("encabezado",

                    new XElement("convenio", datos.convenio),
                    new XElement("proveedor", datos.proveedor),
                    new XElement("codigoRetorno", datos.codigoRetorno),
                    new XElement("mensajeRetorno", datos.mensajeRetorno)),

                new XElement("identificadores",

                    new XElement("requerimiento", datos.requerimiento),
                    new XElement("idepol", datos.idepol),
                    new XElement("codpol", datos.codpol),
                    new XElement("numpol", datos.numpol),
                    new XElement("numcert", datos.numcert),
                    new XElement("dvid", datos.dvid),
                    new XElement("numid", datos.numid)),

                new XElement("valores",

                    new XElement("moneda", datos.moneda),
                    new XElement("total_requerimiento", datos.total_requerimiento),
                    new XElement("vencimiento_requerimiento", datos.vencimiento_requerimiento),
                    new XElement("nombre_cliente", datos.nombre_cliente),
                    new XElement("sistema_requerimiento", datos.sistema_requerimiento),
                    new XElement("numero_cuota", datos.numero_cuota)));


            return xmlfromLINQ.ToString();
        }
        public DataTable XML_DataTable(string resultadoXML)
        {
            DataSet dsResultado = new DataSet();
            DataTable resultado = new DataTable();
            XmlDocument xml = new XmlDocument();

            try
            {
                xml.LoadXml(resultadoXML);

                dsResultado.ReadXml(new XmlNodeReader(xml));
                resultado = dsResultado.Tables[0];
            }
            catch (Exception ex) { }
            return resultado;
        }

        public DataSet XML_DataSet(string resultadoXML)
        {
            DataSet dsResultado = new DataSet();
            DataTable resultado = new DataTable();
            XmlDocument xml = new XmlDocument();

            try
            {
                xml.LoadXml(resultadoXML);

                dsResultado.ReadXml(new XmlNodeReader(xml));

            }
            catch (Exception ex) { }
            return dsResultado;
        }

        [DataContract]
        public class Bitacora_wsTransacLinea
        {
            [DataMember]
            public Int64 ID_TRANSACCION { get; set; }
            [DataMember]
            public string ID_REFERENCIA { get; set; }
            [DataMember]
            public string SISTEMA_USO { get; set; }
            [DataMember]
            public string EMAIL_NOTIFICA { get; set; }
            [DataMember]
            public string HOST { get; set; }
            [DataMember]
            public string PUERTO { get; set; }
            [DataMember]
            public string DNS { get; set; }
            [DataMember]
            public string PATH { get; set; }
            [DataMember]
            public string URL { get; set; }
            [DataMember]
            public string METODO { get; set; }
            [DataMember]
            public string MENSAJE { get; set; }
            [DataMember]
            public string XML_IN { get; set; }
            [DataMember]
            public string SISTEMA { get; set; }
            [DataMember]
            public string USUARIO_ORIGEN { get; set; }
            [DataMember]
            public string CODIGO_USUARIO { get; set; }
            [DataMember]
            public string ERROR { get; set; }
            [DataMember]
            public string XML_OUT { get; set; }
            [DataMember]
            public string MONEDA { get; set; }
            [DataMember]
            public string TOTAL { get; set; }
            [DataMember]
            public Bitacora_DetalleWSTransacLinea Detalle_Bitacora { get; set; }
        }

        [DataContract]
        public class Bitacora_DetalleWSTransacLinea
        {
            [DataMember]
            public Int64 ID_TRANSACCION { get; set; }
            [DataMember]
            public string ID_DETALLE { get; set; }
            [DataMember]
            public string METODO { get; set; }
            [DataMember]
            public string XML_IN { get; set; }
            [DataMember]
            public string MENSAJE { get; set; }
            [DataMember]
            public string PROCEDURE { get; set; }
            [DataMember]
            public string ERROR { get; set; }
            [DataMember]
            public string COBRO_TARJETA { get; set; }
            [DataMember]
            public string REINTEGRO_TARJETA { get; set; }
            [DataMember]
            public string PAGAR_RECIBO { get; set; }
            [DataMember]
            public string XML_OUT { get; set; }
            [DataMember]
            public string FECHA_IN { get; set; }
            [DataMember]
            public string FECHA_OUT { get; set; }
            [DataMember]
            public string EXTRA_DATA { get; set; }
        }


    }
}
