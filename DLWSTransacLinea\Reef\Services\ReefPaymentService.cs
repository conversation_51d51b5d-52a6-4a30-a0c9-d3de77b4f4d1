using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.BusinessLogic;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;
using System.Linq;
using DLWSTransacLinea.Reef.Converters;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para procesar pagos digitales en REEF usando producto cartesiano
    /// </summary>
    public class ReefPaymentService
    {
        private readonly NwtTsyApiClient _apiClient;
        private readonly MonedaService _monedaService;

        public ReefPaymentService()
        {
            _apiClient = new NwtTsyApiClient();
            _monedaService = new MonedaService();
        }

        /// <summary>
        /// Procesa un pago digital simplificado realizando el producto cartesiano entre recibos y medios de pago
        /// </summary>
        /// <param name="requerimientos">Lista de requerimientos/recibos a pagar</param>
        /// <param name="mediosPago">Lista de medios de pago</param>
        /// <param name="encabezado">Información del encabezado de la transacción</param>
        /// <returns>Resultado del procesamiento del pago</returns>
        public async Task<bool> ProcesarPagoDigitalAsync(
            List<xmlTransaccion.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado)
        {
            bool response = false;
            try
            {
                // Validaciones básicas
                if (requerimientos == null || requerimientos.Count == 0)
                {
                    //resultado.MensajeError = "No se proporcionaron requerimientos para procesar";
                    //return resultado;
                }

                if (mediosPago == null || mediosPago.Count == 0)
                {
                    //resultado.MensajeError = "No se proporcionaron medios de pago";
                    //return resultado;
                }

                // Procesar cobranza masiva
                var collectMassiveService = new CollectMassiveService();
                var resultadoCobranza = await collectMassiveService.ProcesarCobranzaMasivaAsync(
                    requerimientos: requerimientos,
                    mediosPago: mediosPago,
                    encabezado: encabezado,
                    bitacora: null // TODO: Pasar la bitácora si está disponible
                );

                if (resultadoCobranza.Exitoso)
                {
                    // La cobranza masiva fue exitosa
                    response = true;
                }
                else
                {
                    // Manejar error en cobranza masiva
                    // TODO: Agregar logging del error
                }
            }
            catch (Exception ex)
            {
            }

            return response;
        }
    }
}
