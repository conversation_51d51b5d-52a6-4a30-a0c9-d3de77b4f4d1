using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Converters
{
    /// <summary>
    /// Clase para convertir objetos de póliza entre diferentes formatos
    /// </summary>
    public class PolizaConverter
    {
        /// <summary>
        /// Convierte un objeto PolizaStructure a un DataTable
        /// </summary>
        /// <param name="poliza">Objeto PolizaStructure a convertir</param>
        /// <returns>DataTable con la información de la póliza</returns>
        public DataTable ConvertPolizaToDataTable(PolizaStructure poliza)
        {
            try
            {
                DataTable dataTable = new DataTable("Poliza");
                dataTable.Columns.Add("idepol", typeof(string));
                dataTable.Columns.Add("numpoliza", typeof(string));
                dataTable.Columns.Add("num_contrato", typeof(string));
                dataTable.Columns.Add("tip_docum", typeof(string));
                dataTable.Columns.Add("cod_docum", typeof(string));
                dataTable.Columns.Add("asegurado", typeof(string));
                dataTable.Columns.Add("estadopoliza", typeof(string));
                dataTable.Columns.Add("iniciovigencia", typeof(string));
                dataTable.Columns.Add("finvigencia", typeof(string));
                dataTable.Columns.Add("mca_provisional", typeof(string));
                dataTable.Columns.Add("certificado", typeof(string));
                dataTable.Columns.Add("sistema", typeof(string));
                dataTable.Columns.Add("poliza_grupo", typeof(string));

                if (poliza != null && poliza.oPlyGniP != null && poliza.oPlyGniP.oPlyGniS != null)
                {
                    DataRow row = dataTable.NewRow();
                    row["idepol"] = poliza.oPlyGniP.oPlyGniS.plyVal;
                    row["numpoliza"] = poliza.oPlyGniP.oPlyGniS.plyVal;
                    row["num_contrato"] = null;

                    var terceroPagador = poliza.oPlyInePT?.Select(item => item.oPlyIneS).FirstOrDefault(x => x.bnfTypNam == "TOMADOR")
                                        ?? poliza.oPlyInePT?.Select(item => item.oPlyIneS).FirstOrDefault();

                    if (terceroPagador != null)
                    {
                        row["tip_docum"] = terceroPagador.thpDcmTypVal;
                        row["cod_docum"] = terceroPagador.thpDcmVal;
                        row["asegurado"] = terceroPagador.cpeNam;
                    }
                    else
                    {
                        Console.WriteLine("No se encontró un pagador.");
                    }

                    DateTime fechaHoy = DateTime.Now;
                    DateTime fechaInicioVigencia = Utils.UnixTimeStampToDateTime(poliza.oPlyGniP.oPlyGniS.plyEfcDat);
                    DateTime fechaFinVigencia = Utils.UnixTimeStampToDateTime(poliza.oPlyGniP.oPlyGniS.plyExpDat);

                    row["iniciovigencia"] = fechaInicioVigencia.ToString("dd/MM/yyyy");
                    row["finvigencia"] = fechaFinVigencia.ToString("dd/MM/yyyy");
                    row["mca_provisional"] = poliza.oPlyGniP.oPlyGniS.prvMvm;
                    row["certificado"] = "1";
                    row["poliza_grupo"] = "N";
                    row["sistema"] = Constants.Systems.REEF;

                    if (poliza.oPlyGniP.oPlyGniS.canPly == "S") { row["estadopoliza"] = "ANU"; }
                    else if (fechaFinVigencia < fechaHoy) { row["estadopoliza"] = "VEN"; }
                    else { row["estadopoliza"] = "ACT"; }

                    dataTable.Rows.Add(row);
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir PolizaStructure a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
