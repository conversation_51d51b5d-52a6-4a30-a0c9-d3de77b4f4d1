using System;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Clase base para todos los clientes API con configuración SSL genérica
    /// </summary>
    public abstract class BaseApiClient : IDisposable
    {
        protected readonly HttpClient _httpClient;
        protected readonly CacheClearService _cacheClearService;
        protected readonly CacheClearService.ApiType _apiType;
        private bool _disposed = false;

        /// <summary>
        /// Constructor base que inicializa el cliente HTTP con configuración SSL
        /// </summary>
        /// <param name="baseUrlKey">Clave del web.config para la URL base</param>
        /// <param name="usernameKey">Clave del web.config para el usuario</param>
        /// <param name="passwordKey">Clave del web.config para la contraseña</param>
        protected BaseApiClient(string baseUrlKey, string usernameKey, string passwordKey)
        {
            try
            {
                // Obtener configuración del web.config
                string baseUrl = ConfigurationManager.AppSettings[baseUrlKey];
                string username = ConfigurationManager.AppSettings[usernameKey];
                string password = ConfigurationManager.AppSettings[passwordKey];

                // Validar configuración
                if (string.IsNullOrEmpty(baseUrl))
                    throw new ConfigurationErrorsException($"No se encontró la configuración '{baseUrlKey}' en web.config");

                // Crear handler con configuración SSL
                var handler = SslConfiguration.CreateConfiguredHandler();

                // Crear cliente HTTP
                _httpClient = new HttpClient(handler)
                {
                    BaseAddress = new Uri(baseUrl),
                    Timeout = TimeSpan.FromMinutes(5) // Timeout de 5 minutos
                };

                // Configurar autenticación básica si se proporcionan credenciales
                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    var byteArray = Encoding.ASCII.GetBytes($"{username}:{password}");
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
                }

                // Configurar headers por defecto
                ConfigureDefaultHeaders();

                // Inicializar servicio de limpieza de caché
                _cacheClearService = new CacheClearService();
                _apiType = CacheClearService.DetermineApiType(baseUrl);

                System.Diagnostics.Debug.WriteLine($"Cliente API inicializado: {baseUrl} (Tipo: {_apiType})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error inicializando cliente API: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Configura los headers por defecto del cliente
        /// </summary>
        protected virtual void ConfigureDefaultHeaders()
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Accept.Clear();
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                // Agregar headers comunes de REEF
                _httpClient.DefaultRequestHeaders.Add("cmpVal", Constants.GetCompany());
                _httpClient.DefaultRequestHeaders.Add("lngVal", Constants.GetLanguage());
                _httpClient.DefaultRequestHeaders.Add("usrVal", Constants.GetUser());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error configurando headers por defecto: {ex.Message}");
            }
        }

        /// <summary>
        /// Crea un HttpRequestMessage con headers básicos configurados
        /// </summary>
        /// <param name="method">Método HTTP</param>
        /// <param name="requestUri">URI de la petición</param>
        /// <returns>HttpRequestMessage configurado</returns>
        protected HttpRequestMessage CreateRequest(HttpMethod method, string requestUri)
        {
            var request = new HttpRequestMessage(method, requestUri);
            
            // Agregar headers específicos si es necesario
            request.Headers.Accept.Clear();
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            
            return request;
        }

        /// <summary>
        /// Ejecuta una petición HTTP de forma segura
        /// </summary>
        /// <param name="request">Petición HTTP</param>
        /// <returns>Respuesta HTTP</returns>
        protected async Task<HttpResponseMessage> SendRequestAsync(HttpRequestMessage request)
        {
            try
            {
                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return response;
            }
            catch (HttpRequestException ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error en petición HTTP: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error inesperado en petición: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Ejecuta una petición HTTP con retry automático y limpieza de caché en caso de fallo
        /// </summary>
        /// <param name="requestFactory">Función que crea la petición HTTP</param>
        /// <param name="maxRetries">Número máximo de reintentos (por defecto 1)</param>
        /// <returns>Respuesta HTTP</returns>
        protected async Task<HttpResponseMessage> SendRequestWithRetryAsync(Func<HttpRequestMessage> requestFactory, int maxRetries = 1)
        {
            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    using (var request = requestFactory())
                    {
                        var response = await _httpClient.SendAsync(request);
                        response.EnsureSuccessStatusCode();
                        return response;
                    }
                }
                catch (HttpRequestException ex) when (attempt < maxRetries && ShouldRetryWithCacheClear(ex))
                {
                    lastException = ex;
                    System.Diagnostics.Debug.WriteLine($"Intento {attempt + 1} falló, limpiando caché y reintentando: {ex.Message}");

                    // Intentar limpiar caché antes del siguiente intento
                    //await TryClearCacheAsync();

                    // Esperar un poco antes del siguiente intento
                    await Task.Delay(GetRetryDelayFromConfig());
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error en petición HTTP (intento {attempt + 1}): {ex.Message}");
                    throw;
                }
            }

            // Si llegamos aquí, todos los intentos fallaron
            throw new HttpRequestException($"Falló después de {maxRetries + 1} intentos. Último error: {lastException?.Message}", lastException);
        }

        /// <summary>
        /// Determina si se debe reintentar la petición con limpieza de caché basado en el tipo de excepción
        /// </summary>
        /// <param name="ex">Excepción HTTP</param>
        /// <returns>True si se debe reintentar con limpieza de caché</returns>
        private bool ShouldRetryWithCacheClear(HttpRequestException ex)
        {
            if (ex == null) return false;

            var message = ex.Message?.ToLowerInvariant() ?? "";

            // Condiciones que indican problemas de caché o conexión
            return message.Contains("timeout") ||
                   message.Contains("connection") ||
                   message.Contains("network") ||
                   message.Contains("cache") ||
                   message.Contains("502") ||  // Bad Gateway
                   message.Contains("503") ||  // Service Unavailable
                   message.Contains("504");    // Gateway Timeout
        }

        /// <summary>
        /// Obtiene el número máximo de reintentos desde la configuración
        /// </summary>
        /// <returns>Número máximo de reintentos</returns>
        protected int GetMaxRetriesFromConfig()
        {
            try
            {
                string configValue = ConfigurationManager.AppSettings["CacheClear_MaxRetries"] ?? "1";
                if (int.TryParse(configValue, out int maxRetries))
                {
                    return Math.Max(0, Math.Min(maxRetries, 3)); // Limitar entre 0 y 3
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error leyendo configuración de reintentos: {ex.Message}");
            }
            return 1; // Valor por defecto
        }

        /// <summary>
        /// Obtiene el delay entre reintentos desde la configuración
        /// </summary>
        /// <returns>Delay en milisegundos</returns>
        protected int GetRetryDelayFromConfig()
        {
            try
            {
                string configValue = ConfigurationManager.AppSettings["CacheClear_RetryDelay"] ?? "1000";
                if (int.TryParse(configValue, out int delay))
                {
                    return Math.Max(500, Math.Min(delay, 5000)); // Limitar entre 500ms y 5s
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error leyendo configuración de delay: {ex.Message}");
            }
            return 1000; // Valor por defecto
        }

        /// <summary>
        /// Ejecuta una petición GET con retry automático y limpieza de caché
        /// </summary>
        /// <param name="requestUri">URI de la petición</param>
        /// <param name="maxRetries">Número máximo de reintentos (si es -1, usa configuración)</param>
        /// <returns>Respuesta HTTP</returns>
        protected async Task<HttpResponseMessage> GetWithRetryAsync(string requestUri, int maxRetries = -1)
        {
            if (maxRetries == -1) maxRetries = GetMaxRetriesFromConfig();
            return await SendRequestWithRetryAsync(() => new HttpRequestMessage(HttpMethod.Get, requestUri), maxRetries);
        }

        /// <summary>
        /// Ejecuta una petición POST con retry automático y limpieza de caché
        /// </summary>
        /// <param name="requestUri">URI de la petición</param>
        /// <param name="content">Contenido de la petición</param>
        /// <param name="maxRetries">Número máximo de reintentos (si es -1, usa configuración)</param>
        /// <returns>Respuesta HTTP</returns>
        protected async Task<HttpResponseMessage> PostWithRetryAsync(string requestUri, HttpContent content, int maxRetries = -1)
        {
            if (maxRetries == -1) maxRetries = GetMaxRetriesFromConfig();
            return await SendRequestWithRetryAsync(() =>
            {
                var request = new HttpRequestMessage(HttpMethod.Post, requestUri)
                {
                    Content = content
                };
                return request;
            }, maxRetries);
        }

        /// <summary>
        /// Ejecuta una petición GET con retry automático, limpieza de caché y devuelve el contenido como string
        /// </summary>
        /// <param name="requestUri">URI de la petición</param>
        /// <param name="maxRetries">Número máximo de reintentos (si es -1, usa configuración)</param>
        /// <returns>Contenido de la respuesta como string</returns>
        protected async Task<string> GetStringWithRetryAsync(string requestUri, int maxRetries = -1)
        {
            using (var response = await GetWithRetryAsync(requestUri, maxRetries))
            {
                return await response.Content.ReadAsStringAsync();
            }
        }

        /// <summary>
        /// Ejecuta una petición POST con retry automático, limpieza de caché y devuelve el contenido como string
        /// </summary>
        /// <param name="requestUri">URI de la petición</param>
        /// <param name="content">Contenido de la petición</param>
        /// <param name="maxRetries">Número máximo de reintentos (si es -1, usa configuración)</param>
        /// <returns>Contenido de la respuesta como string</returns>
        protected async Task<string> PostStringWithRetryAsync(string requestUri, HttpContent content, int maxRetries = -1)
        {
            using (var response = await PostWithRetryAsync(requestUri, content, maxRetries))
            {
                return await response.Content.ReadAsStringAsync();
            }
        }

        /// <summary>
        /// Libera los recursos utilizados por el cliente
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libera los recursos utilizados por el cliente
        /// </summary>
        /// <param name="disposing">True si se está liberando explícitamente</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _httpClient?.Dispose();
                _cacheClearService?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// Destructor
        /// </summary>
        ~BaseApiClient()
        {
            Dispose(false);
        }
    }
}
