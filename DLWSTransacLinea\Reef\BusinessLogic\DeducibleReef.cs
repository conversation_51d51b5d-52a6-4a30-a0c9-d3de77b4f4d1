﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Reef.Services;
using DLWSTransacLinea.Reef.Models;
using DLWSTransacLinea.Reef.Converters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;

namespace DLWSTransacLinea.Reef.BusinessLogic
{
    public class DeducibleReef
    {
        /// <summary>
        /// Proceso para realizar el calculo del pago del deducible
        /// </summary>
        /// <param name="p_num_sini">Número de siniestro</param>
        /// <param name="p_mca_factura">Marca de factura</param>
        /// <param name="p_num_cuota">Número de cuota</param>
        /// <returns>DataTable con la información del cálculo del deducible</returns>
        public DataTable CalcularLiquidacion(string p_num_sini,
                                             string p_mca_factura = "N",
                                             string p_num_cuota = "1")
        {
            try
            {
                DeducibleService deducibleService = new DeducibleService();
                DeducibleConverter deducibleConverter = new DeducibleConverter();

                MontoDeducibleStructure montoDeducible = deducibleService.ConsultarMontoDeducible(p_mca_factura, p_num_cuota, p_num_sini);

                if (montoDeducible != null)
                {
                    return deducibleConverter.ConvertMontoDeducibleToDataTable(montoDeducible);
                }

                return new DataTable();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al calcular liquidación: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// Proceso que valida criterios si procede el pago o no
        /// </summary>
        /// <param name="p_num_sini">Numero de siniestro</param>
        /// <returns></returns>
        public string ValidarLiquidacion(string p_num_sini)
        {
            try
            {
                DeducibleService deducibleService = new DeducibleService();

                ValidacionDeducibleStructure validacion = deducibleService.ValidarEstadoDeducible(numSini: p_num_sini);

                if (validacion != null && !string.IsNullOrEmpty(validacion.Resp))
                {
                    return validacion.Resp;
                }

                return "No se pudo validar el estado del deducible";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al validar liquidación: {ex.Message}");
                return $"Error: {ex.Message}";
            }
        }

        /// <summary>
        /// Proceso para generar el numero de deducible
        /// </summary>
        /// <param name="p_num_sini"></param>
        /// <param name="p_mca_factura"></param>
        /// <param name="p_num_cuota"></param>
        /// <param name="p_cod_cia"></param>
        /// <returns></returns>
        public long CrearLiquidacion(string p_num_sini,
                                     string p_mca_factura = "N",
                                     string p_num_cuota = "1",
                                     Estructuras.Bitacora_wsTransacLinea Bitacora = null)
        {
            return 0;
            /*
             long resultado = 0;
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            #region Bitacora
            if (Bitacora == null)
            {
                Bitacora = new Estructuras.Bitacora_wsTransacLinea();
            }

            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            DLWSTransacLinea.Cores.MySQLLogger Oper = new DLWSTransacLinea.Cores.MySQLLogger();
            Detalle_Bitacora.PROCEDURE = "ts_k_utils_mgt.f_dev_genera_dedu";
            Detalle_Bitacora.METODO = "Core_TW_CrearLiquidacion";
            Detalle_Bitacora.XML_IN = new XElement("DATOS",
                                        new XElement("NUM_SINIESTRO", p_num_sini),
                                        new XElement("MCA_FACTURA", p_mca_factura),
                                        new XElement("NUM_CUOTA", p_num_cuota)).ToString();
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            Detalle_Bitacora.REINTEGRO_TARJETA = "0";
            Detalle_Bitacora.COBRO_TARJETA = "0";
            #endregion

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "ts_k_utils_mgt.f_dev_genera_dedu";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_cod_cia", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("p_num_sini", OracleDbType.Int64);
                OracleParameter p3 = new OracleParameter("p_mca_factura", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("p_num_cuota", OracleDbType.Varchar2);
                OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.Int64);

                p1.Value = p_cod_cia ?? "2";
                p2.Value = p_num_sini;
                p3.Value = p_mca_factura ?? "N";
                p4.Value = p_num_cuota ?? "1";
                retorno.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(retorno);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);

                cmd.ExecuteNonQuery();

                resultado = Int64.Parse(cmd.Parameters["Return_Value"].Value.ToString());

                Detalle_Bitacora.ERROR = resultado > 0 ? "0" : "1";
                Detalle_Bitacora.PAGAR_RECIBO = resultado > 0 ? "1" : "0";
                Detalle_Bitacora.XML_OUT = new XElement("DATOS", new XElement("NUM_DEDUCIBLE", resultado)).ToString();
                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.Replace(Environment.NewLine, "");
                Detalle_Bitacora.XML_OUT = new XElement("DATOS", new XElement("ERROR", ex.Message)).ToString();
            }
            finally
            {
                conexion.Close();
                Detalle_Bitacora.ID_TRANSACCION = Bitacora.ID_TRANSACCION;
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                Oper.Insert_DetalleBitacora(Bitacora);
            }

            return resultado;
             */
        }

        /// <summary>
        /// Consulta la información de un deducible
        /// </summary>
        /// <param name="numLiquidacion">Número de liquidación</param>
        /// <returns></returns>
        public DeducibleInfoStructure consultarLiquidaciones(string numLiquidacion)
        {
            DeducibleInfoStructure dtInformacion = new DeducibleInfoStructure();

            try
            {
                DeducibleService deducibleService = new DeducibleService();
                dtInformacion = deducibleService.ConsultaInformacionDeducible(numLiq: numLiquidacion);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            return dtInformacion;
        }
    }
}
