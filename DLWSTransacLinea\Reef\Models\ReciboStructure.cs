﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Models
{
    public class ReciboStructure
    {
        public ORcpErcC oRcpErcC { get; set; }
    }

    public class ORcpErcC
    {
        public List<ORcpPmiPT> oRcpPmiPT { get; set; }
        public ORcpPmrP oRcpPmrP { get; set; }
    }

    public class ORcpPmiPT
    {
        public ORcpPmiS oRcpPmiS { get; set; }
    }

    public class ORcpPmiS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int enrSqn { get; set; }
        public int aplVal { get; set; }
        public int aplEnrSqn { get; set; }
        public int inmVal { get; set; }
        public int rcpVal { get; set; }
        public object enrIsuDat { get; set; }
        public double rcpAmn { get; set; }
        public double nt0Amn { get; set; }
        public long srcAmn { get; set; }
        public long taxAmn { get; set; }
        public long bnsAmn { get; set; }
        public double cmsAmn { get; set; }
        public double totCmsAmn { get; set; }
        public string agnMdf { get; set; }
        public string pmsMdf { get; set; }
        public double itrAmn { get; set; }
        public double itrTaxAmn { get; set; }
    }

    public class ORcpPmrP
    {
        public ORcpPmrS oRcpPmrS { get; set; }
    }

    public class ORcpPmrS
    {
        public int cmpVal { get; set; }
        public string plyVal { get; set; }
        public int aplVal { get; set; }
        public int rcpVal { get; set; }
        public long rcpEfcDat { get; set; }
        public long rcpExpDat { get; set; }
        public string mnrTypVal { get; set; }
        public string mnrTypNam { get; set; }
        public string mnrVal { get; set; }
        public string mnrCpeNam { get; set; }
        public string rcpStsTypVal { get; set; }
        public string rcpStsNam { get; set; }
        public string rmtTypVal { get; set; }
        public string rmtTypNam { get; set; }
        public long rmtDat { get; set; }
        public long acgDat { get; set; }
        public long valDat { get; set; }
        public int crnVal { get; set; }
        public string crnNam { get; set; }
        public long exrVal { get; set; }
        public string cinTypVal { get; set; }
        public string cinTypNam { get; set; }
        public int thrLvlVal { get; set; }
        public int agnVal { get; set; }
        public string ntcVal { get; set; }
        /// <summary>
        /// Tipo de aviso de pago
        /// </summary>
        public string pymDcmTypVal { get; set; }
        /// <summary>
        /// Número de aviso de pago
        /// </summary>
        public string pymDcmVal { get; set; }
        public string cmsDnt { get; set; }
        public long pymExpDat { get; set; }
        /// <summary>
        /// Importe del recibo
        /// </summary>
        public double rcpAmn { get; set; }
        public long? pndAmn { get; set; }
    }
}
