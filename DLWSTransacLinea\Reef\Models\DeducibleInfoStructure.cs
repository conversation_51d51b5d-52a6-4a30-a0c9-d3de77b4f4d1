using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la información del deducible
    /// </summary>
    public class DeducibleInfoStructure
    {
        [JsonProperty("codActTercero")]
        public int CodActTercero { get; set; }

        [JsonProperty("codDocum")]
        public string CodDocum { get; set; }

        [JsonProperty("codMonLiq")]
        public int CodMonLiq { get; set; }

        [JsonProperty("codMonLiqIso")]
        public string CodMonLiqIso { get; set; }

        [JsonProperty("codMonPago")]
        public int CodMonPago { get; set; }

        [JsonProperty("codMonPagoIso")]
        public string CodMonPagoIso { get; set; }

        [JsonProperty("codNivel3")]
        public int CodNivel3 { get; set; }

        [JsonProperty("codRamo")]
        public int CodRamo { get; set; }

        [JsonProperty("codSector")]
        public int CodSector { get; set; }

        [JsonProperty("codTercero")]
        public string CodTercero { get; set; }

        [JsonProperty("fecEstPago")]
        public string FecEstPago { get; set; }

        [JsonProperty("fecLiq")]
        public string FecLiq { get; set; }

        [JsonProperty("fecPago")]
        public string FecPago { get; set; }

        [JsonProperty("impIva")]
        public decimal ImpIva { get; set; }

        [JsonProperty("impLiq")]
        public decimal ImpLiq { get; set; }

        [JsonProperty("impLiqNeto")]
        public decimal ImpLiqNeto { get; set; }

        [JsonProperty("nomActTercero")]
        public string NomActTercero { get; set; }

        [JsonProperty("nomNivel3")]
        public string NomNivel3 { get; set; }

        [JsonProperty("nomSector")]
        public string NomSector { get; set; }

        [JsonProperty("nomTercero")]
        public string NomTercero { get; set; }

        [JsonProperty("numDecimales")]
        public int NumDecimales { get; set; }

        [JsonProperty("numExp")]
        public int NumExp { get; set; }

        [JsonProperty("numLiq")]
        public long NumLiq { get; set; }

        [JsonProperty("numPoliza")]
        public string NumPoliza { get; set; }

        [JsonProperty("numSini")]
        public long NumSini { get; set; }

        [JsonProperty("obs")]
        public string Obs { get; set; }

        [JsonProperty("tipDocto")]
        public string TipDocto { get; set; }

        [JsonProperty("tipDocum")]
        public string TipDocum { get; set; }

        [JsonProperty("valCambio")]
        public decimal ValCambio { get; set; }
    }
}
