﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_Ienviar_correo&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_Ienviar_correo" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpsBinding_Ienviar_correo&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="BasicHttpsBinding_Ienviar_correo" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://***********/ws_comunica_d/Wcf/enviar_correo.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_Ienviar_correo&quot; contract=&quot;WSComunica.Ienviar_correo&quot; name=&quot;BasicHttpBinding_Ienviar_correo&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://***********/ws_comunica_d/Wcf/enviar_correo.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_Ienviar_correo&quot; contract=&quot;WSComunica.Ienviar_correo&quot; name=&quot;BasicHttpBinding_Ienviar_correo&quot; /&gt;" contractName="WSComunica.Ienviar_correo" name="BasicHttpBinding_Ienviar_correo" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://app2.mapfre.com.gt/ws_comunica_d/Wcf/enviar_correo.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_Ienviar_correo&quot; contract=&quot;WSComunica.Ienviar_correo&quot; name=&quot;BasicHttpsBinding_Ienviar_correo&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://app2.mapfre.com.gt/ws_comunica_d/Wcf/enviar_correo.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_Ienviar_correo&quot; contract=&quot;WSComunica.Ienviar_correo&quot; name=&quot;BasicHttpsBinding_Ienviar_correo&quot; /&gt;" contractName="WSComunica.Ienviar_correo" name="BasicHttpsBinding_Ienviar_correo" />
  </endpoints>
</configurationSnapshot>