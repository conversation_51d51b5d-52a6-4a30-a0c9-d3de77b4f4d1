using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con recibos
    /// </summary>
    public class AvisoPagoService
    {
        private readonly NwtTsyApiClient _apiClient;
        private readonly NwtGtApiClient _gtApiClient;
        private readonly AvisoPagoConverter _converter;
        private readonly AvisoPagoPolizaGrupoConverter _polizaGrupoConverter;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public AvisoPagoService()
        {
            _apiClient = new NwtTsyApiClient();
            _gtApiClient = new NwtGtApiClient();
            _converter = new AvisoPagoConverter();
            _polizaGrupoConverter = new AvisoPagoPolizaGrupoConverter();
        }

        /// <summary>
        /// Recupera la información de un recibo
        /// </summary>
        /// <param name="pymDcmTypVal"></param>
        /// <param name="pymDcmVal"></param>
        /// <returns></returns>
        public AvisoPagoStructure ConsultaInformacionAvisoPago(string pymDcmTypVal, string pymDcmVal)
        {
            try
            {
                Task<string> task = _apiClient.GetReceiptGroupAsync(pymDcmTypVal: pymDcmTypVal, pymDcmVal: pymDcmVal);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<AvisoPagoStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar póliza por suplemento: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Conversion de AvisoPagoStructure a DataTable
        /// </summary>
        /// <param name="avisoPagoStructure">Información del aviso</param>
        /// <param name="currenciesList">Listado de monedas</param>
        /// <param name="responsablePago">Informacion del responsable de pago</param>
        /// <returns></returns>
        public DataTable ConvertAvisoPagoToDataTable(AvisoPagoStructure avisoPagoStructure, List<MonedaStructure> currenciesList, ResponsablePagoStructure responsablePago)
        {
            return _converter.ConvertAvisoPagoToDataTable(avisoPagoStructure: avisoPagoStructure, currenciesList: currenciesList, responsablePago: responsablePago);
        }

        /// <summary>
        /// Consulta los avisos de pago de una póliza grupo
        /// </summary>
        /// <param name="numContrato">Número de contrato</param>
        /// <param name="numPolizaGrupo">Número de póliza grupo</param>
        /// <returns>Lista de avisos de pago de la póliza grupo</returns>
        public List<AvisoPagoPolizaGrupoStructure> ConsultarAvisosPagoPolizaGrupo(string numContrato, string numPolizaGrupo)
        {
            try
            {
                Task<string> task = _gtApiClient.GetAvisoPagoPolizaGrupoAsync(numContrato, numPolizaGrupo);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<List<AvisoPagoPolizaGrupoStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar avisos de pago de póliza grupo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Convierte la lista de avisos de pago de póliza grupo a DataTable
        /// </summary>
        /// <param name="avisosPago">Lista de avisos de pago de póliza grupo</param>
        /// <param name="currenciesList">Lista de monedas</param>
        /// <returns>DataTable con los datos de los avisos de pago</returns>
        public DataTable ConvertAvisosPagoPolizaGrupoToDataTable(List<AvisoPagoPolizaGrupoStructure> avisosPago, List<MonedaStructure> currenciesList)
        {
            return _polizaGrupoConverter.ConvertAvisosPagoPolizaGrupoToDataTable(avisosPago, currenciesList);
        }

    }
}
