﻿using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Converters
{
    public class AvisoPagoConverter
    {
        /// <summary>
        /// Convierte la estructura AvisoPagoStructure a DataTable
        /// </summary>
        /// <param name="avisoPagoStructure">Información aviso</param>
        /// <param name="currenciesList">Listado de monedas</param>
        /// <param name="responsablePago">Informacion del responsable de pago</param>
        /// <returns>DataTable con los datos del recibo</returns>
        public DataTable ConvertAvisoPagoToDataTable(AvisoPagoStructure avisoPagoStructure, List<MonedaStructure> currenciesList, ResponsablePagoStructure responsablePago)
        {
            try
            {
                DataTable dataTable = new DataTable("Recibo");
                dataTable.Columns.Add("REQUERIMIENTO", typeof(string));
                dataTable.Columns.Add("POLIZA", typeof(string));
                dataTable.Columns.Add("ID_MONEDA", typeof(string));
                dataTable.Columns.Add("MONEDA", typeof(string));
                dataTable.Columns.Add("TOTAL", typeof(string));
                dataTable.Columns.Add("FECHA_VENCIMIENTO", typeof(string));
                dataTable.Columns.Add("PAGADOR", typeof(string));
                dataTable.Columns.Add("SISTEMA", typeof(string));
                dataTable.Columns.Add("PAGO", typeof(string));
                dataTable.Columns.Add("STSFACT", typeof(string));
                dataTable.Columns.Add("IDEPOL", typeof(string));
                dataTable.Columns.Add("FECHA_COBRO", typeof(string));
                dataTable.Columns.Add("ASIGNA_FACTURA", typeof(string));
                dataTable.Columns.Add("CODFACT", typeof(string));
                dataTable.Columns.Add("EMAIL", typeof(string));
                dataTable.Columns.Add("DIREC_COBRO", typeof(string));
                dataTable.Columns.Add("ES_AVISO", typeof(string));

                if (avisoPagoStructure != null && avisoPagoStructure?.rcrStsTypVal != "AN")
                {
                    var monedaInformacion = currenciesList.Where(x => x.oCrnCrnS.crnVal == avisoPagoStructure.crnVal).FirstOrDefault();
                    DateTime fechaVencimientoAviso = Utils.UnixTimeStampToDateTime(avisoPagoStructure.expDat);

                    DataRow row = dataTable.NewRow();
                    row["REQUERIMIENTO"] = avisoPagoStructure.pymDcmVal;
                    row["POLIZA"] = avisoPagoStructure.gppVal;
                    row["ID_MONEDA"] = avisoPagoStructure.crnVal;
                    row["MONEDA"] = monedaInformacion.oCrnCrnS.sdrCrnVal;
                    row["TOTAL"] = avisoPagoStructure.dcmAmn;
                    row["FECHA_VENCIMIENTO"] = fechaVencimientoAviso.ToString("dd/MM/yyyy");
                    row["SISTEMA"] = Constants.Systems.REEF;
                    row["PAGO"] = "1"; //PENDIENTE
                    row["STSFACT"] = avisoPagoStructure.rcrStsTypVal;
                    row["IDEPOL"] = avisoPagoStructure.gppVal;
                    row["ASIGNA_FACTURA"] = "S";
                    row["ES_AVISO"] = "S";

                    if (avisoPagoStructure.rcrStsTypVal == "CT")
                    {
                        DateTime fechaPagoAvisoPago = Utils.UnixTimeStampToDateTime(avisoPagoStructure.mvmDat);
                        row["FECHA_COBRO"] = fechaPagoAvisoPago.ToString("dd/MM/yyyy");
                    }

                    if (responsablePago != null)
                    {
                        row["PAGADOR"] = responsablePago.NombreCompleto;
                        row["CODFACT"] = "PENDIENTE";//PENDIENTE
                        row["EMAIL"] = responsablePago.Email;
                        row["DIREC_COBRO"] = responsablePago.DirecCobro;
                    }

                    dataTable.Rows.Add(row);
                }
                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir ORcpErcC a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
