using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la respuesta del monto del deducible
    /// </summary>
    public class MontoDeducibleStructure
    {
        [JsonProperty("moneda")]
        public string Moneda { get; set; }

        [JsonProperty("mtoDeducible")]
        public decimal MtoDeducible { get; set; }

        [JsonProperty("mtoIva")]
        public decimal MtoIva { get; set; }

        [JsonProperty("mtoCuotas")]
        public decimal MtoCuotas { get; set; }

        [JsonProperty("mtoTotal")]
        public decimal MtoTotal { get; set; }
    }
}
