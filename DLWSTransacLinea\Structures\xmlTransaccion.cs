﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DLWSTransacLinea.Structures
{
    public class xmlTransaccion
    {
        #region Public Methods

        public XElement generarXmlTransaccion(_transaccion _datos)
        {
            var xmlfromLINQ = new XElement("datos",

                    new XElement("encabezado",
                        new XElement("convenio", _datos.datos.encabezado.convenio),
                        new XElement("proveedor", _datos.datos.encabezado.proveedor),
                        new XElement("codigoRetorno", _datos.datos.encabezado.codigoRetorno),
                        new XElement("mensajeRetorno", _datos.datos.encabezado.mensajeRetorno),
                        new XElement("usuarioOrigen", _datos.datos.encabezado.usuarioOrigen),
                        new XElement("codigoCajero", _datos.datos.encabezado.codigoCajero),
                        new XElement("tipoBusqueda", _datos.datos.encabezado.tipoBusqueda),
                        new XElement("tipoTransaccion", _datos.datos.encabezado.tipoTransaccion),
                        new XElement("autorizacionProveedor", _datos.datos.encabezado.autorizacionProveedor),
                        new XElement("autorizacionBanco", _datos.datos.encabezado.autorizacionBanco),
                        new XElement("idTransaccion", _datos.datos.encabezado.idTransaccion),
                        new XElement("sistemaUso", _datos.datos.encabezado.sistemaUso),
                        new XElement("idReferencia", _datos.datos.encabezado.idReferencia)),

                    new XElement("identificadores",
                        new XElement("requerimiento", _datos.datos.identificadores.requerimiento),
                        new XElement("idepol", _datos.datos.identificadores.idepol),
                        new XElement("codpol", _datos.datos.identificadores.codpol),
                        new XElement("numpol", _datos.datos.identificadores.numpol),
                        new XElement("numcert", _datos.datos.identificadores.numcert),
                        new XElement("dvid", _datos.datos.identificadores.dvid),
                        new XElement("numid", _datos.datos.identificadores.numid),
                        new XElement("nit", _datos.datos.identificadores.nit),
                        new XElement("sistema", _datos.datos.identificadores.sistema),
                        new XElement("vigencia_inicial", _datos.datos.identificadores.vigencia_inicial),
                        new XElement("es_poliza_grupo", _datos.datos.identificadores.es_poliza_grupo),
                        new XElement("tip_docum", _datos.datos.identificadores.tip_docum),
                        new XElement("cod_docum", _datos.datos.identificadores.cod_docum),
                        new XElement("num_sini", _datos.datos.identificadores.num_sini),
                        new XElement("mca_factura", _datos.datos.identificadores.mca_factura),
                        new XElement("num_cuotas", _datos.datos.identificadores.num_cuotas)),

                    new XElement("polizas",
                        ((_datos.datos.polizas != null) ?
                        from p in _datos.datos.polizas.poliza
                        select new XElement("poliza",
                            new XElement("numPoliza", p.numPoliza),
                            new XElement("asegurado", p.asegurado),
                            new XElement("estadoPoliza", p.estadoPoliza),
                            new XElement("inicioVigencia", p.inicioVigencia),
                            new XElement("finVigencia", p.finVigencia),
                            new XElement("certificado", p.certificado),
                            new XElement("idepol", p.idepol),
                            new XElement("sistema", p.sistema),
                            new XElement("mca_provisional", p.mca_provisional),
                            new XElement("poliza_grupo", p.poliza_grupo),
                            new XElement("num_contrato", p.num_contrato),
                            new XElement("tip_docum", p.tip_docum),
                            new XElement("cod_docum", p.cod_docum)
                            ) : null)),

                    new XElement("requerimientos",
                        ((_datos.datos.requerimientos != null) ?
                        from r in _datos.datos.requerimientos.requerimiento
                        select new XElement("requerimiento",
                            new XElement("numeroRequerimiento", r.numeroRequerimiento),
                            new XElement("id_moneda", r.id_moneda),
                            new XElement("moneda", r.moneda),
                            new XElement("totalRequerimiento", r.totalRequerimiento),
                            new XElement("vencimientoRequerimiento", r.vencimientoRequerimiento),
                            new XElement("tip_docum", r.tip_docum),
                            new XElement("cod_docum", r.cod_docum),
                            new XElement("nombrePagador", r.nombrePagador),
                            new XElement("sistema", r.sistema),
                            new XElement("nitPagador", r.nitPagador),
                            new XElement("poliza", r.poliza),
                            new XElement("estado", r.estado),
                            new XElement("numeroCuota", r.numeroCuota),
                            new XElement("idepol", r.idepol),
                            new XElement("fechaCobroRequerimiento", r.fechaCobroRequerimiento),
                            new XElement("asignaFactura", r.asignaFactura),
                            new XElement("codFact", r.codFact),
                            new XElement("email", r.email),
                            new XElement("direc_cobro", r.direc_cobro),
                            new XElement("es_aviso", r.es_aviso)
                            ) : null)),
                    new XElement("mediosPago",
                        ((_datos.datos.mediosPago != null) ?
                         from m in _datos.datos.mediosPago.medio
                         select new XElement("medio",

                             new XElement("tipoPago", m.tipoPago),
                             new XElement("descEntidadFinanciera", m.descEntidadFinanciera),
                             new XElement("codEntidadFinanciera", m.codEntidadFinanciera),
                             new XElement("numeroReferencia", m.numeroReferencia),
                             new XElement("codMoneda", m.codMoneda),
                             new XElement("monto", m.monto),
                             new XElement("numeroAutorizacion", m.numeroAutorizacion),
                             new XElement("fechaDeposito", m.fechaDeposito),
                             new XElement("numeroDocumento", m.numeroDocumento),
                             new XElement("fecha_cheque", m.fecha_cheque),
                             new XElement("tipoTarjeta", m.tipoTarjeta),
                             new XElement("codigoTarjeta", m.codigoTarjeta),
                             new XElement("CUENTA_SIMPLIFICADA", m.CUENTA_SIMPLIFICADA),
                             new XElement("DESCRIPCION", m.DESCRIPCION)) : null)),

                    new XElement("liquidaciones",
                        ((_datos.datos.liquidaciones != null) ?
                         from m in _datos.datos.liquidaciones.liquidacion
                         select new XElement("liquidacion",
                             new XElement("moneda", m.moneda),
                             new XElement("mto_deducible", m.mto_deducible),
                             new XElement("mto_iva", m.mto_iva),
                             new XElement("mto_cuotas", m.mto_cuotas),
                             new XElement("mto_total", m.mto_total),
                             new XElement("num_deducible", m.num_deducible)) : null)),

                    new XElement("resumenPago",
                            new XElement("requerimientos",
                            ((_datos.datos.requerimientos != null) ?
                             from res in _datos.datos.requerimientos.requerimiento
                             select new XElement("requerimiento",
                                new XElement("numeroRequerimiento", res.numeroRequerimiento),
                                new XElement("codFact", res.codFact),
                                new XElement("numFact", res.numFact),
                                new XElement("cae", res.cae),
                                new XElement("reling", res.reling)
                                ) : null))),
                    new XElement("mensaje",
                            new XElement("tipo", _datos.datos.mensaje.tipo),
                            new XElement("titulo", _datos.datos.mensaje.titulo),
                            new XElement("mensaje", _datos.datos.mensaje.mensaje),
                            new XElement("codigo", _datos.datos.mensaje.codigo))
                    );

            return xmlfromLINQ;
        }

        #endregion Public Methods

        #region Public Classes

        public class _encabezado
        {
            #region Public Properties

            public string autorizacionBanco { get; set; }
            public string autorizacionProveedor { get; set; }
            public string codigoCajero { get; set; }
            public string codigoRetorno { get; set; }
            public string convenio { get; set; }
            public string idReferencia { get; set; }
            public string idTransaccion { get; set; }
            public string mensajeRetorno { get; set; }
            public string proveedor { get; set; }
            public string sistemaUso { get; set; }
            public string tipoBusqueda { get; set; }
            public string tipoTransaccion { get; set; }
            public string usuarioOrigen { get; set; }
            public string esAviso { get; set; }

            #endregion Public Properties
        }

        public class _estructuraTransaccion
        {
            #region Public Properties

            public _encabezado encabezado { get; set; }
            public _identificadores identificadores { get; set; }
            public _medio mediosPago { get; set; }
            public _poliza polizas { get; set; }
            public _requerimiento requerimientos { get; set; }
            public _liquidacion liquidaciones { get; set; }
            public _requerimientos resumenPago { get; set; }
            public _mensaje mensaje { get; set; }
            #endregion Public Properties

            public _estructuraTransaccion()
            {
                mensaje = new _mensaje();
            }
        }

        public class _identificadores
        {
            #region Public Properties

            public string codpol { get; set; }
            public string dvid { get; set; }
            public string idepol { get; set; }
            public string nit { get; set; }
            public string numcert { get; set; }
            public string numid { get; set; }
            public string numpol { get; set; }
            public string requerimiento { get; set; }
            public string sistema { get; set; }

            /// <summary>
            /// Busqueda de requerimientos de tronweb
            /// </summary>
            public string vigencia_inicial { get; set; }
            public string es_poliza_grupo { get; set; }
            public string tip_docum { get; set; }
            public string cod_docum { get; set; }
            public string num_sini { get; set; }
            public string num_cuotas { get; set; }
            public string mca_factura { get; set; }

            #endregion Public Properties
        }

        public class _medio
        {
            #region Public Properties

            public List<medio> medio { get; set; }

            #endregion Public Properties
        }

        public class _poliza
        {
            #region Public Properties

            public List<poliza> poliza { get; set; }

            #endregion Public Properties
        }

        public class _requerimiento
        {
            #region Public Properties

            public List<requerimiento> requerimiento { get; set; }

            #endregion Public Properties
        }

        public class _liquidacion
        {
            #region Public Properties
            public List<liquidacion> liquidacion { get; set; }
            #endregion Public Properties

            public _liquidacion()
            {
                liquidacion = new List<liquidacion>();
            }
        }


        /// <summary>
        /// Estructura para retornar un mensaje especificando el tipo
        /// </summary>
        public class _mensaje
        {
            public string tipo { get; set; }
            public string titulo { get; set; }
            public string mensaje { get; set; }
            public string codigo { get; set; }
        }

        public class _requerimientos
        {
            #region Public Properties

            public List<requerimiento> requerimiento { get; set; }

            #endregion Public Properties
        }

        public class _resumenPago
        {
            #region Public Properties

            public string cobrados { get; set; }
            public string idTransaccion { get; set; }
            public string monedaTransaccion { get; set; }
            public string pendientes { get; set; }
            public string totalCobrado { get; set; }
            public string totalDevolucion { get; set; }

            #endregion Public Properties
        }

        public class _transaccion
        {
            #region Public Properties

            public _estructuraTransaccion datos { get; set; }

            #endregion Public Properties
        }

        public class medio
        {
            #region Public Properties

            public string codEntidadFinanciera { get; set; }
            public string codigoTarjeta { get; set; }
            public string codMoneda { get; set; }
            public string CUENTA_SIMPLIFICADA { get; set; }
            public string descEntidadFinanciera { get; set; }
            public string DESCRIPCION { get; set; }
            public string fecha_cheque { get; set; }
            public string fechaDeposito { get; set; }
            public string monto { get; set; }
            public string numeroAutorizacion { get; set; }
            public string numeroDocumento { get; set; }
            public string numeroReferencia { get; set; }
            public string sistema { get; set; }
            public string tipoPago { get; set; }
            public string tipoTarjeta { get; set; }

            #endregion Public Properties
        }

        public class poliza
        {
            #region Public Properties

            public string asegurado { get; set; }
            public string certificado { get; set; }
            public string estadoPoliza { get; set; }
            public string finVigencia { get; set; }
            public string idepol { get; set; }
            public string inicioVigencia { get; set; }
            public string mca_provisional { get; set; }
            public string numPoliza { get; set; }
            public string sistema { get; set; }
            /// <summary>
            /// INDICADOR POLIZA GRUPO
            /// </summary>
            public string poliza_grupo { get; set; }
            public string num_contrato { get; set; }
            public string tip_docum { get; set; }
            public string cod_docum { get; set; }
            #endregion Public Properties
        }

        public class requerimiento
        {
            #region Public Properties

            public string asignaFactura { get; set; }
            public string cae { get; set; }
            public string cobrado { get; set; }
            public string codFact { get; set; }
            public string direc_cobro { get; set; }
            public string email { get; set; }
            public string estado { get; set; }
            public string fechaCobroRequerimiento { get; set; }
            public string idepol { get; set; }
            public string id_moneda { get; set; }
            public string moneda { get; set; }
            public string tip_docum { get; set; }
            public string cod_docum { get; set; }
            public string nitPagador { get; set; }
            public string nombrePagador { get; set; }
            public string numeroCuota { get; set; }
            public string numeroRequerimiento { get; set; }
            public string numFact { get; set; }
            public string poliza { get; set; }
            public string reling { get; set; }
            public string sistema { get; set; }
            public string totalRequerimiento { get; set; }
            public string vencimientoRequerimiento { get; set; }
            public string es_aviso { get; set; }

            #endregion Public Properties

            public requerimiento()
            {
                es_aviso = "N";
            }
        }

        public class liquidacion
        {
            public string moneda { get; set; }
            public string mto_deducible { get; set; }
            public string mto_iva { get; set; }
            public string mto_cuotas { get; set; }
            public string mto_total { get; set; }
            public long num_deducible { get; set; }
        }

        #endregion Public Classes
    }
}