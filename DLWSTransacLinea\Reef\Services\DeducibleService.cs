using System;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con deducibles
    /// </summary>
    public class DeducibleService
    {
        private readonly NwtGtApiClient _apiClient;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public DeducibleService()
        {
            _apiClient = new NwtGtApiClient();
        }

        /// <summary>
        /// Consulta la información del deducible
        /// </summary>
        /// <param name="numLiq">Número de liquidación</param>
        /// <returns>Información del deducible</returns>
        public DeducibleInfoStructure ConsultaInformacionDeducible(string numLiq)
        {
            try
            {
                Task<string> task = _apiClient.GetDeducibleInfoAsync(numLiq);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<DeducibleInfoStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar la información del deducible: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Valida el estado del deducible
        /// </summary>
        /// <param name="numSini">Número de siniestro</param>
        /// <returns>Respuesta de validación del deducible</returns>
        public ValidacionDeducibleStructure ValidarEstadoDeducible(string numSini)
        {
            try
            {
                Task<string> task = _apiClient.ValidateDeducibleStatusAsync(numSini);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<ValidacionDeducibleStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al validar el estado del deducible: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta el monto del deducible
        /// </summary>
        /// <param name="mcaFactura">Marca de factura</param>
        /// <param name="numCuota">Número de cuota</param>
        /// <param name="numSini">Número de siniestro</param>
        /// <returns>Información del monto del deducible</returns>
        public MontoDeducibleStructure ConsultarMontoDeducible(string mcaFactura, string numCuota, string numSini)
        {
            try
            {
                Task<string> task = _apiClient.GetMontoDeducibleAsync(mcaFactura, numCuota, numSini);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<MontoDeducibleStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar el monto del deducible: {ex.Message}");
                return null;
            }
        }
    }
}
