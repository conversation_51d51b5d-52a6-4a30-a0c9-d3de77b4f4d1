using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la respuesta del endpoint get_contratos_poliza_grupo
    /// </summary>
    public class ContratoPolizaGrupoStructure
    {
        /// <summary>
        /// Código de la compañía
        /// </summary>
        [JsonProperty("codCia")]
        public int CodCia { get; set; }

        /// <summary>
        /// Número de póliza
        /// </summary>
        [JsonProperty("numPoliza")]
        public string NumPoliza { get; set; }

        /// <summary>
        /// Número de contrato
        /// </summary>
        [JsonProperty("numContrato")]
        public long NumContrato { get; set; }

        /// <summary>
        /// Tipo de póliza
        /// </summary>
        [JsonProperty("tipPoliza")]
        public string TipPoliza { get; set; }

        /// <summary>
        /// Marca de riesgos
        /// </summary>
        [JsonProperty("mcaRiesgos")]
        public string McaRies<PERSON> { get; set; }

        /// <summary>
        /// Fecha de vencimiento de la póliza
        /// </summary>
        [JsonProperty("fecVctoPoliza")]
        public string FecVctoPoliza { get; set; }

        /// <summary>
        /// Cantidad de avisos
        /// </summary>
        [JsonProperty("cantAviso")]
        public int CantAviso { get; set; }

        /// <summary>
        /// Cantidad de pólizas
        /// </summary>
        [JsonProperty("cantPoliza")]
        public int CantPoliza { get; set; }
    }
}
