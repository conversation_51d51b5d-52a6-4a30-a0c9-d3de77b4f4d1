﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Structures
{
    [XmlRoot]
    public class xmlBitacora
    {
        [XmlElement]
        public string p_cod_cia { get; set; }
        [XmlElement]
        public string p_num_recibo { get; set; }
        [XmlElement]
        public string p_result_id { get; set; }
        [XmlElement]
        public string p_cod_mot_resul { get; set; }
        [XmlElement]
        public string p_fec_cita { get; set; }
        [XmlElement]
        public string p_observaciones { get; set; }
        [XmlElement]
        public string p_sistema_origen { get; set; }
        [XmlElement]
        public string p_fec_llamada { get; set; }
    }
}
