using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con recibos
    /// </summary>
    public class MonedaService
    {
        private readonly NwtCmnApiClient _apiClient;
        private readonly ReciboConverter _converter;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public MonedaService()
        {
            _apiClient = new NwtCmnApiClient();
            _converter = new ReciboConverter();
        }

        /// <summary>
        /// Recupera la información de monedas
        /// </summary>
        /// <returns></returns>
        public List<MonedaStructure> ConsultaMonedas()
        {
            try
            {
                Task<string> task = _apiClient.GetCurrenciesAsync();
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<List<MonedaStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar las monedas: {ex.Message}");
                return null;
            }
        }
    }
}
