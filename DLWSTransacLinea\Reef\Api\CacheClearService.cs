using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Servicio para limpiar caché de las diferentes APIs cuando hay fallos de conexión
    /// </summary>
    public class CacheClearService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private bool _disposed = false;

        /// <summary>
        /// Tipos de API disponibles
        /// </summary>
        public enum ApiType
        {
            GT,     // Guatemala API
            ISU,    // Emisión API
            TSY,    // Tesorería API
            CMN,    // Comunes API
            THP     // Third Party API
        }

        /// <summary>
        /// Determina el tipo de API basado en la URL base
        /// </summary>
        /// <param name="baseUrl">URL base del cliente API</param>
        /// <returns>Tipo de API correspondiente</returns>
        public static ApiType DetermineApiType(string baseUrl)
        {
            if (string.IsNullOrEmpty(baseUrl))
                throw new ArgumentException("La URL base no puede ser nula o vacía", nameof(baseUrl));

            baseUrl = baseUrl.ToLowerInvariant();

            if (baseUrl.Contains("nwt_api_gt_be-web") || baseUrl.Contains("localhost:8085"))
                return ApiType.GT;
            else if (baseUrl.Contains("nwt_isu_api_be-web"))
                return ApiType.ISU;
            else if (baseUrl.Contains("nwt_tsy_api_be-web"))
                return ApiType.TSY;
            else if (baseUrl.Contains("nwt_cmn_api_be-web"))
                return ApiType.CMN;
            else if (baseUrl.Contains("nwt_thp_api_be-web"))
                return ApiType.THP;
            else
                throw new ArgumentException($"No se pudo determinar el tipo de API para la URL: {baseUrl}", nameof(baseUrl));
        }

        /// <summary>
        /// Libera los recursos utilizados por el servicio
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libera los recursos utilizados por el servicio
        /// </summary>
        /// <param name="disposing">True si se está liberando explícitamente</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// Destructor
        /// </summary>
        ~CacheClearService()
        {
            Dispose(false);
        }
    }
}
