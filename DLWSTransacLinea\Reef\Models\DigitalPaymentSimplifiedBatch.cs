using System;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para el procesamiento de pago digital simplificado por lotes
    /// </summary>
    public class DigitalPaymentSimplifiedBatch
    {
        /// <summary>
        /// Fecha de Cobro del Recibo. Se Usa Como Fecha Valor
        /// </summary>
        public long? CloDat { get; set; }

        /// <summary>
        /// Tipo Situación Cobro
        /// </summary>
        public string CloStsTypVal { get; set; }

        /// <summary>
        /// Compañía
        /// </summary>
        public int? CmpVal { get; set; }

        /// <summary>
        /// Tipo Anticipo
        /// </summary>
        public string AdvTypVal { get; set; }

        /// <summary>
        /// Anticipo
        /// </summary>
        public string AdvVal { get; set; }

        /// <summary>
        /// Importe del Cobro
        /// </summary>
        public double? CloAmn { get; set; }

        /// <summary>
        /// Moneda Cobro
        /// </summary>
        public int? CloCrnVal { get; set; }

        /// <summary>
        /// Descripción movimiento
        /// </summary>
        public string CosMvmNam { get; set; }

        /// <summary>
        /// Cajero
        /// </summary>
        public string CshVal { get; set; }

        /// <summary>
        /// Idioma
        /// </summary>
        public string LngVal { get; set; }

        /// <summary>
        /// Valor Enmascarado
        /// </summary>
        public string MskVal { get; set; }

        /// <summary>
        /// Clob recibido de la pasarela de pago
        /// </summary>
        public object PgwTxtVal { get; set; }

        /// <summary>
        /// Pasarela de Pago
        /// </summary>
        public string PgwVal { get; set; }

        /// <summary>
        /// Número de póliza
        /// </summary>
        public string PlyVal { get; set; }

        /// <summary>
        /// Tipo del Documento de Pago
        /// </summary>
        public string PymDcmTypVal { get; set; }

        /// <summary>
        /// Documento del Tercero del Pago
        /// </summary>
        public string PymDcmVal { get; set; }

        /// <summary>
        /// Presupuesto
        /// </summary>
        public string QtnVal { get; set; }

        /// <summary>
        /// Recibo
        /// </summary>
        public decimal? RcpVal { get; set; }

        /// <summary>
        /// Aviso de pago
        /// </summary>
        public decimal? RcpGrpVal { get; set; }

        /// <summary>
        /// Cuenta Simplificada
        /// </summary>
        public string SmfAcoVal { get; set; }

        /// <summary>
        /// Liquidación
        /// </summary>
        public long? StlVal { get; set; }

        /// <summary>
        /// Token Medio Cobro Pago y Cobro
        /// </summary>
        public string TknVal { get; set; }

        /// <summary>
        /// Transacción
        /// </summary>
        public string TnsVal { get; set; }

        /// <summary>
        /// Usuario
        /// </summary>
        public string UsrVal { get; set; }
    }
}
