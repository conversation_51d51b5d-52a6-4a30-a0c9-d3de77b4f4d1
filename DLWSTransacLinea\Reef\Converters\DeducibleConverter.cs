using System;
using System.Data;
using DLWSTransacLinea.Reef.Models;

namespace DLWSTransacLinea.Reef.Converters
{
    /// <summary>
    /// Convertidor para estructuras de deducible
    /// </summary>
    public class DeducibleConverter
    {
        /// <summary>
        /// Convierte la estructura MontoDeducibleStructure a DataTable
        /// </summary>
        /// <param name="montoDeducible">Información del monto del deducible</param>
        /// <returns>DataTable con los datos del deducible</returns>
        public DataTable ConvertMontoDeducibleToDataTable(MontoDeducibleStructure montoDeducible)
        {
            try
            {
                DataTable dataTable = new DataTable("Deducible");
                
                dataTable.Columns.Add("moneda", typeof(string));
                dataTable.Columns.Add("mto_deducible", typeof(decimal));
                dataTable.Columns.Add("mto_iva", typeof(decimal));
                dataTable.Columns.Add("mto_cuotas", typeof(decimal));
                dataTable.Columns.Add("mto_total", typeof(decimal));

                if (montoDeducible != null)
                {
                    DataRow row = dataTable.NewRow();
                    row["moneda"] = montoDeducible.Moneda ?? string.Empty;
                    row["mto_deducible"] = montoDeducible.MtoDeducible;
                    row["mto_iva"] = montoDeducible.MtoIva;
                    row["mto_cuotas"] = montoDeducible.MtoCuotas;
                    row["mto_total"] = montoDeducible.MtoTotal;

                    dataTable.Rows.Add(row);
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir MontoDeducibleStructure a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
