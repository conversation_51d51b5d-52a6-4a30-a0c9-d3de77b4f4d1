﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DLWSTransacLinea.WSComunica {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="dato_correo", Namespace="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras")]
    [System.SerializableAttribute()]
    public partial class dato_correo : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Tuple<string, string, byte[]>[] adjuntoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string asuntoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string contenidoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string[] copiaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string[] copia_ocultaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Text.Encoding encodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool htmlField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string id_contenidoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string[] paraField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Collections.Generic.Dictionary<string, string> parametroField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string usuarioField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Tuple<string, string, byte[]>[] adjunto {
            get {
                return this.adjuntoField;
            }
            set {
                if ((object.ReferenceEquals(this.adjuntoField, value) != true)) {
                    this.adjuntoField = value;
                    this.RaisePropertyChanged("adjunto");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string asunto {
            get {
                return this.asuntoField;
            }
            set {
                if ((object.ReferenceEquals(this.asuntoField, value) != true)) {
                    this.asuntoField = value;
                    this.RaisePropertyChanged("asunto");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string contenido {
            get {
                return this.contenidoField;
            }
            set {
                if ((object.ReferenceEquals(this.contenidoField, value) != true)) {
                    this.contenidoField = value;
                    this.RaisePropertyChanged("contenido");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] copia {
            get {
                return this.copiaField;
            }
            set {
                if ((object.ReferenceEquals(this.copiaField, value) != true)) {
                    this.copiaField = value;
                    this.RaisePropertyChanged("copia");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] copia_oculta {
            get {
                return this.copia_ocultaField;
            }
            set {
                if ((object.ReferenceEquals(this.copia_ocultaField, value) != true)) {
                    this.copia_ocultaField = value;
                    this.RaisePropertyChanged("copia_oculta");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Text.Encoding encode {
            get {
                return this.encodeField;
            }
            set {
                if ((object.ReferenceEquals(this.encodeField, value) != true)) {
                    this.encodeField = value;
                    this.RaisePropertyChanged("encode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool html {
            get {
                return this.htmlField;
            }
            set {
                if ((this.htmlField.Equals(value) != true)) {
                    this.htmlField = value;
                    this.RaisePropertyChanged("html");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string id_contenido {
            get {
                return this.id_contenidoField;
            }
            set {
                if ((object.ReferenceEquals(this.id_contenidoField, value) != true)) {
                    this.id_contenidoField = value;
                    this.RaisePropertyChanged("id_contenido");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] para {
            get {
                return this.paraField;
            }
            set {
                if ((object.ReferenceEquals(this.paraField, value) != true)) {
                    this.paraField = value;
                    this.RaisePropertyChanged("para");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.Dictionary<string, string> parametro {
            get {
                return this.parametroField;
            }
            set {
                if ((object.ReferenceEquals(this.parametroField, value) != true)) {
                    this.parametroField = value;
                    this.RaisePropertyChanged("parametro");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string usuario {
            get {
                return this.usuarioField;
            }
            set {
                if ((object.ReferenceEquals(this.usuarioField, value) != true)) {
                    this.usuarioField = value;
                    this.RaisePropertyChanged("usuario");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CodePageDataItem", Namespace="http://schemas.datacontract.org/2004/07/System.Globalization")]
    [System.SerializableAttribute()]
    public partial class CodePageDataItem : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string m_bodyNameField;
        
        private int m_dataIndexField;
        
        private uint m_flagsField;
        
        private string m_headerNameField;
        
        private int m_uiFamilyCodePageField;
        
        private string m_webNameField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string m_bodyName {
            get {
                return this.m_bodyNameField;
            }
            set {
                if ((object.ReferenceEquals(this.m_bodyNameField, value) != true)) {
                    this.m_bodyNameField = value;
                    this.RaisePropertyChanged("m_bodyName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int m_dataIndex {
            get {
                return this.m_dataIndexField;
            }
            set {
                if ((this.m_dataIndexField.Equals(value) != true)) {
                    this.m_dataIndexField = value;
                    this.RaisePropertyChanged("m_dataIndex");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public uint m_flags {
            get {
                return this.m_flagsField;
            }
            set {
                if ((this.m_flagsField.Equals(value) != true)) {
                    this.m_flagsField = value;
                    this.RaisePropertyChanged("m_flags");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string m_headerName {
            get {
                return this.m_headerNameField;
            }
            set {
                if ((object.ReferenceEquals(this.m_headerNameField, value) != true)) {
                    this.m_headerNameField = value;
                    this.RaisePropertyChanged("m_headerName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int m_uiFamilyCodePage {
            get {
                return this.m_uiFamilyCodePageField;
            }
            set {
                if ((this.m_uiFamilyCodePageField.Equals(value) != true)) {
                    this.m_uiFamilyCodePageField = value;
                    this.RaisePropertyChanged("m_uiFamilyCodePage");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string m_webName {
            get {
                return this.m_webNameField;
            }
            set {
                if ((object.ReferenceEquals(this.m_webNameField, value) != true)) {
                    this.m_webNameField = value;
                    this.RaisePropertyChanged("m_webName");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="WSComunica.Ienviar_correo")]
    public interface Ienviar_correo {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Ienviar_correo/doEnviar", ReplyAction="http://tempuri.org/Ienviar_correo/doEnviarResponse")]
        string doEnviar(string p_id_correo, DLWSTransacLinea.WSComunica.dato_correo datos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Ienviar_correo/doEnviar", ReplyAction="http://tempuri.org/Ienviar_correo/doEnviarResponse")]
        System.Threading.Tasks.Task<string> doEnviarAsync(string p_id_correo, DLWSTransacLinea.WSComunica.dato_correo datos);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface Ienviar_correoChannel : DLWSTransacLinea.WSComunica.Ienviar_correo, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class Ienviar_correoClient : System.ServiceModel.ClientBase<DLWSTransacLinea.WSComunica.Ienviar_correo>, DLWSTransacLinea.WSComunica.Ienviar_correo {
        
        public Ienviar_correoClient() {
        }
        
        public Ienviar_correoClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public Ienviar_correoClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Ienviar_correoClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Ienviar_correoClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string doEnviar(string p_id_correo, DLWSTransacLinea.WSComunica.dato_correo datos) {
            return base.Channel.doEnviar(p_id_correo, datos);
        }
        
        public System.Threading.Tasks.Task<string> doEnviarAsync(string p_id_correo, DLWSTransacLinea.WSComunica.dato_correo datos) {
            return base.Channel.doEnviarAsync(p_id_correo, datos);
        }
    }
}
