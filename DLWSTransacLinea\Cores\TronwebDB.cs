﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Structures;
using Oracle.DataAccess.Client;
using Oracle.DataAccess.Types;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;
using static DLWSTransacLinea.Comunes.Estructuras;

namespace DLWSTransacLinea.Cores
{
    public class TronwebDB
    {
        /// <summary>
        /// Recupera la poliza y el numero de cuota
        /// </summary>
        /// <param name="pReciboTron"></param>
        /// <returns></returns>
        public DataTable ObtenerCuotaRecibo(string pReciboTron)
        {
            DataTable datos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                conexion = clConexion.abrirConexionOracleTRONWEB();
                string query = "SELECT NUM_POLIZA, NUM_CUOTA FROM ******** A WHERE A.NUM_RECIBO = " + pReciboTron;

                OracleDataAdapter adapter = new OracleDataAdapter(query, conexion);
                adapter.Fill(datos);
                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return datos;
        }

        public string ValidaOrigenRequerimiento(string requerimiento)
        {
            string respuesta = string.Empty;

            DataTable datosAcsel = new DataTable();
            DataTable datosTronWeb = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            string queryAcsel = "SELECT IDEFACT FROM  factura WHERE FECSTS BETWEEN  ADD_MONTHS(SYSDATE,-36) AND SYSDATE " +
                " AND IDEFACT = " + requerimiento;
            string queryTronWeb = "SELECT NUM_RECIBO FROM ******** WHERE  NUM_RECIBO = " + requerimiento;
            string query_consultar = string.Empty;

            OracleDataAdapter adapter = new OracleDataAdapter(queryAcsel, conexion);

            try
            {
                adapter.Fill(datosAcsel);

                if (datosAcsel.Rows.Count > 0)
                {
                    query_consultar = queryAcsel;
                    respuesta = Constants.Systems.ACSEL;
                    conexion.Close();
                }
                else
                {
                    conexion = clConexion.abrirConexionOracleTRONWEB();
                    adapter = new OracleDataAdapter(queryTronWeb, conexion);
                    adapter.Fill(datosTronWeb);

                    if (datosTronWeb.Rows.Count > 0)
                    {
                        query_consultar = queryTronWeb;
                        respuesta = Constants.Systems.TRONWEB;
                        conexion.Close();
                    }
                }

                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return respuesta;
        }

        /// <summary>
        /// string 1: sistema, recibo tron (migrada)
        /// </summary>
        /// <param name="requerimiento"></param>
        /// <returns></returns>
        public List<Tuple<string, string, string>> ValidaSistemaRequerimiento(string requerimiento)
        {
            List<Tuple<string, string, string>> resultado = new List<Tuple<string, string, string>>();

            string v_sistema = string.Empty;
            string v_recibo_tron = string.Empty;

            DataTable datosAcsel = new DataTable();
            DataTable datosTronWeb = new DataTable();
            DataTable datosTronWebRecibos = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();
            conexion = clConexion.abrirConexionOracleAcsel();

            string queryAcsel = "SELECT " +
                                "  a.idefact, " +
                                "  a.stsfact, " +
                                "  a.idepol, " +
                                "  d.numcert, " +
                                "  e.poltron, " +
                                "  c.numgiro " +
                                "FROM factura a, " +
                                "  ACSEL.acreencia b, " +
                                "  ACSEL.giros_financiamiento c, " +
                                "  ACSEL.cond_financiamiento d, " +
                                "  ACSEL.bitacora_migracion e " +
                                "WHERE a.idefact = b.idefact " +
                                "AND b.numacre   = c.numacre " +
                                "AND c.numfinanc = d.numfinanc " +
                                "AND d.idepol    = e.idepol(+) " +
                                "AND d.numcert   = e.numcert(+) " +
                                "AND a.FECSTS BETWEEN ADD_MONTHS(SYSDATE, -36) AND SYSDATE " +
                                "AND a.IDEFACT   = " + requerimiento + " " +
                                "UNION " +
                                "SELECT a.idefact, " +
                                "  a.stsfact, " +
                                "  a.idepol, " +
                                "  NULL, " +
                                "  NULL, " +
                                "  NULL " +
                                "FROM ACSEL.factura a, " +
                                "  ACSEL.acreencia b " +
                                "WHERE b.idefact = a.idefact " +
                                "AND a.idefact = " + requerimiento + " " +
                                "AND b.tipoacre IN ('DED') ";
            //Inclusion de avisos
            string queryTronWeb = "SELECT " +
                                   "  TO_CHAR(NUM_RECIBO) CODIGO, " +
                                   "  'N' AVISO " +
                                   " FROM ******** " +
                                   " WHERE NUM_RECIBO =  " + requerimiento +
                                   " UNION " +
                                   " SELECT " +
                                   "   num_aviso, " +
                                   "   'S' " +
                                   " FROM a2990700 " +
                                   " WHERE num_aviso = " + requerimiento;

            try
            {
                OracleDataAdapter adapter = new OracleDataAdapter(queryAcsel, conexion);
                adapter.Fill(datosAcsel);

                if (datosAcsel.Rows.Count > 0)
                {
                    try
                    {
                        string poliza_tron = datosAcsel.Rows[0]["poltron"].ToString();
                        string sts_poliza = datosAcsel.Rows[0]["stsfact"].ToString();
                        string num_cuota = datosAcsel.Rows[0]["numgiro"].ToString();

                        if (!string.IsNullOrWhiteSpace(poliza_tron) && sts_poliza == "MIG")
                        {
                            string queryTronRecibo = "SELECT " +
                                                     "  MIN(NUM_RECIBO) NUM_RECIBO " +
                                                     "FROM ******** " +
                                                     "WHERE NUM_POLIZA = " + poliza_tron +
                                                     "  AND NUM_CUOTA = " + num_cuota;

                            conexion = clConexion.abrirConexionOracleTRONWEB();
                            adapter = new OracleDataAdapter(queryTronRecibo, conexion);
                            adapter.Fill(datosTronWebRecibos);

                            if (datosTronWebRecibos.Rows.Count > 0)
                            {
                                resultado.Add(new Tuple<string, string, string>(Constants.Systems.TRONWEB, datosTronWebRecibos.Rows[0]["NUM_RECIBO"].ToString(), "N"));
                            }
                        }
                        else
                        {
                            resultado.Add(new Tuple<string, string, string>(Constants.Systems.ACSEL, null, "N"));
                        }
                    }
                    catch (Exception) { }
                    finally
                    {
                        conexion.Close();
                    }
                }
                else
                {
                    conexion = clConexion.abrirConexionOracleTRONWEB();
                    adapter = new OracleDataAdapter(queryTronWeb, conexion);
                    adapter.Fill(datosTronWeb);

                    if (datosTronWeb.Rows.Count > 0)
                    {
                        for (int i = 0; i < datosTronWeb.Rows.Count; i++)
                        {
                            resultado.Add(new Tuple<string, string, string>(Constants.Systems.TRONWEB, null, datosTronWeb.Rows[i]["AVISO"].ToString()));
                        }
                    }
                    conexion.Close();
                }

                conexion.Close();
            }
            catch (OracleException) { conexion.Close(); }
            finally { conexion.Close(); }

            return resultado;
        }

        public DataTable BuscarRequerimientos_TW(string pNumPol = null,
                                                string pVigenciaIni = null,
                                                string pPolizaGrupo = null,
                                                string pNoDocto = null,
                                                string pEsAviso = null,
                                                string pTipDocum = null,
                                                string pCodDocum = null)
        {
            DataTable resultados = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleAcsel();
                cmd.Connection = conexion;
                cmd.CommandText = "PKG_TRANSAC_ONLINE.GET_REQUERIMIENTO_TW";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("pNumPol", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("pVigenciaIni", OracleDbType.Date);
                OracleParameter p3 = new OracleParameter("p_no_documento", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("p_es_poliza_grupo", OracleDbType.Varchar2);
                OracleParameter p5 = new OracleParameter("p_es_aviso", OracleDbType.Varchar2);
                OracleParameter p6 = new OracleParameter("p_tip_docum", OracleDbType.Varchar2);
                OracleParameter p7 = new OracleParameter("p_cod_docum", OracleDbType.Varchar2);
                OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                p1.Value = pNumPol;
                if (pVigenciaIni == null) { p2.Value = DBNull.Value; }
                else { p2.Value = DateTime.Parse(pVigenciaIni); }
                p3.Value = pNoDocto;
                p4.Value = (string.IsNullOrEmpty(pPolizaGrupo) ? "N" : pPolizaGrupo);
                p5.Value = pEsAviso;
                p6.Value = pTipDocum;
                p7.Value = pCodDocum;
                retorno.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(retorno);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);
                cmd.Parameters.Add(p5);
                cmd.Parameters.Add(p6);
                cmd.Parameters.Add(p7);

                resultados.Load(cmd.ExecuteReader());

                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultados;
        }

        public string CobrarTronWeb(string xmlDetalleCobro)
        {
            string resultado = string.Empty;

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            //xmlDetalleCobro = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "Gc_K_Ws_Cobro_Movil_Mgt.f_cobrar_recibo";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_recibos", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Clob);

                p1.Value = xmlDetalleCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                resultado = (((OracleClob)cmd.Parameters["Return_Value"].Value)).Value;
                conexion.Close();

            }
            catch (OracleException ex)
            {
                conexion.Close();
                resultado = "ERROR " + ex.Message;
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }
        public DataSet CobrarTronWebv2(string xmlDetalleCobro, Bitacora_wsTransacLinea Bitacora)
        {
            string resultado = string.Empty;
            DataSet dtsRespuesta = new DataSet();
            MySQLLogger mySQLLogger = new MySQLLogger();
            Estructuras estructuras = new Estructuras();

            Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.XML_IN = xmlDetalleCobro;
            Detalle_Bitacora.METODO = "cobrarTronWebv2";
            Detalle_Bitacora.PROCEDURE = "GC_K_WS_COBRO_ONLINE.F_COBRAR_RECIBO";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            //xmlDetalleCobro = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "GC_K_WS_COBRO_ONLINE.F_COBRAR_RECIBO";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_recibos", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Clob);

                p1.Value = xmlDetalleCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                resultado = (((OracleClob)cmd.Parameters["Return_Value"].Value)).Value;

                conexion.Close();

                dtsRespuesta = estructuras.XML_DataSet(resultado);

                if (dtsRespuesta.Tables.Count > 0)
                {
                    if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "0")
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "1";
                        Detalle_Bitacora.ERROR = "0";
                    }
                    else
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "0";
                        Detalle_Bitacora.ERROR = "1";
                        Detalle_Bitacora.MENSAJE = dtsRespuesta.Tables[0].Rows[0]["Descripcion"].ToString();
                    }
                }
                Detalle_Bitacora.XML_OUT = resultado.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            catch (OracleException ex)
            {
                conexion.Close();

                resultado = "ERROR " + ex.Message;
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return dtsRespuesta;
        }
        public DataSet CobrarDeducibleT(string xmlDetalleCobro, Bitacora_wsTransacLinea Bitacora)
        {
            string resultado = string.Empty;
            DataSet dtsRespuesta = new DataSet();
            Estructuras estructuras = new Estructuras();
            MySQLLogger mySQLLogger = new MySQLLogger();

            Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.XML_IN = xmlDetalleCobro;
            Detalle_Bitacora.METODO = "cobrarDeducibleT";
            Detalle_Bitacora.PROCEDURE = "gc_k_ws_cobro_liq_online.f_cobra_liquidacion";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            //xmlDetalleCobro = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "gc_k_ws_cobro_liq_online.f_cobra_liquidacion";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_liquidacion", OracleDbType.XmlType);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Clob);

                p1.Value = xmlDetalleCobro;
                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                resultado = (((OracleClob)cmd.Parameters["Return_Value"].Value)).Value;

                conexion.Close();

                dtsRespuesta = estructuras.XML_DataSet(resultado);

                if (dtsRespuesta.Tables.Count > 0)
                {
                    if (dtsRespuesta.Tables[0].Rows[0]["Codigo"].ToString() == "0")
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "1";
                        Detalle_Bitacora.ERROR = "0";
                    }
                    else
                    {
                        Detalle_Bitacora.PAGAR_RECIBO = "0";
                        Detalle_Bitacora.ERROR = "1";
                        Detalle_Bitacora.MENSAJE = dtsRespuesta.Tables[0].Rows[0]["Descripcion"].ToString();
                    }
                }
                Detalle_Bitacora.XML_OUT = resultado.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            catch (OracleException ex)
            {
                conexion.Close();

                resultado = "ERROR " + ex.Message;
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return dtsRespuesta;
        }
        public string RemesaReciboTronWeb(string xmlDetalleBitacora, Bitacora_wsTransacLinea Bitacora = null)
        {
            //xmlDetalleBitacora = "<DATOS><ENCABEZADO><SISTEMA>TRONWEB</SISTEMA><EQUIPO>001</EQUIPO><NUMEPAGO/></ENCABEZADO><REQUERIMIENTO><NUMRECI>32346</NUMRECI><SERIE/><NUMEFACT/></REQUERIMIENTO><DOCING><TIPOPAGO>EFE</TIPOPAGO><ENTFINAN/><NUMREF/><CODMONEDA>Q</CODMONEDA><MONTO>647.07</MONTO><NUMAUTORIZA/><FECHA_CHQ/><TIPO_TAR/><CODIGO_TAR/><NUMREF_TAR/></DOCING></DATOS>";

            MySQLLogger mySQLLogger = new MySQLLogger();

            xmlBitacora datosBitacora = new xmlBitacora();
            XmlSerializer serializer = new XmlSerializer(typeof(xmlBitacora));

            using (StringReader reader = new StringReader(xmlDetalleBitacora))
            {
                datosBitacora = (xmlBitacora)serializer.Deserialize(reader);
            }

            #region BUSQUEDA RECIBO REMESADO
            //PRIMERO SE DEBE VALIDAR SI EL RECIBO SE ENCUENTRA REMESADO
            string query = "SELECT * FROM A5029010 WHERE NUM_RECIBO = " + datosBitacora.p_num_recibo;
            DataTable registroRemesado = new DataTable();
            #endregion

            string respuesta = "";
            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.XML_IN = xmlDetalleBitacora;
            Detalle_Bitacora.METODO = "remesaReciboTronWeb";
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();

                OracleDataAdapter adapter = new OracleDataAdapter(query, conexion);
                adapter.Fill(registroRemesado);

                // SI EL RECIBO NO HA SIDO REMESADO SE PROCECE A ACTUALIZAR LA BITACORA
                // PARA LUEGO PROCEDER A COBRAR EL RECIBO
                if (registroRemesado.Rows.Count == 0)
                {
                    cmd.Connection = conexion;
                    cmd.CommandText = "Gc_Cobro_Movil_Pruebas.p_actualiza_bitacora";
                    cmd.CommandType = CommandType.StoredProcedure;

                    OracleParameter p1 = new OracleParameter("p_cod_cia", OracleDbType.Int16);
                    OracleParameter p2 = new OracleParameter("p_num_recibo", OracleDbType.Int64);
                    OracleParameter p3 = new OracleParameter("p_result_id", OracleDbType.Int64);
                    OracleParameter p4 = new OracleParameter("p_cod_mot_resul", OracleDbType.Varchar2);
                    OracleParameter p5 = new OracleParameter("p_fec_cita", OracleDbType.Date);
                    OracleParameter p6 = new OracleParameter("p_observaciones", OracleDbType.Varchar2);
                    OracleParameter p7 = new OracleParameter("p_sistema_origen", OracleDbType.Varchar2);
                    OracleParameter p8 = new OracleParameter("p_fec_llamada", OracleDbType.Date);

                    p1.Value = datosBitacora.p_cod_cia;
                    p2.Value = datosBitacora.p_num_recibo;
                    p3.Value = datosBitacora.p_result_id;
                    p4.Value = datosBitacora.p_cod_mot_resul;
                    p5.Value = DateTime.Parse(datosBitacora.p_fec_cita);
                    p6.Value = datosBitacora.p_observaciones;
                    p7.Value = datosBitacora.p_sistema_origen;
                    p8.Value = DateTime.Parse(datosBitacora.p_fec_llamada);

                    cmd.Parameters.Add(p1);
                    cmd.Parameters.Add(p2);
                    cmd.Parameters.Add(p3);
                    cmd.Parameters.Add(p4);
                    cmd.Parameters.Add(p5);
                    cmd.Parameters.Add(p6);
                    cmd.Parameters.Add(p7);
                    cmd.Parameters.Add(p8);

                    cmd.ExecuteNonQuery();

                    respuesta = "1";

                    Detalle_Bitacora.ERROR = "0";
                    Detalle_Bitacora.PROCEDURE = "Gc_Cobro_Movil_Pruebas_Mgt.p_actualiza_bitacora";

                }
                else
                {
                    respuesta = "0";
                    Detalle_Bitacora.ERROR = "0";
                    Detalle_Bitacora.PROCEDURE = query;
                }

                conexion.Close();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            catch (OracleException ex)
            {
                conexion.Close();

                respuesta = "0";
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.MENSAJE = ex.Message.ToString();
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }
            finally
            {
                conexion.Close();
            }

            return respuesta;
        }
        public string GenerarXMLRemesaRecibo(xmlBitacora bitacora)
        {
            var xmlfromLINQ = new XElement("xmlBitacora",
                        new XElement("p_cod_cia", bitacora.p_cod_cia),
                        new XElement("p_num_recibo", bitacora.p_num_recibo),
                        new XElement("p_result_id", bitacora.p_result_id),
                        new XElement("p_cod_mot_resul", bitacora.p_cod_mot_resul),
                        new XElement("p_fec_cita", bitacora.p_fec_cita),
                        new XElement("p_observaciones", bitacora.p_observaciones),
                        new XElement("p_sistema_origen", bitacora.p_sistema_origen),
                        new XElement("p_fec_llamada", bitacora.p_fec_llamada)
                        );

            return xmlfromLINQ.ToString();
        }

        public DataTable ConsultarLiquidaciones(string numLiquidacion)
        {
            DataTable resultados = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "gc_f_info_liquidacion";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_num_liq", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                p1.Value = numLiquidacion;

                p2.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                resultados.Load(cmd.ExecuteReader());
            }
            catch (OracleException)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultados;
        }

        public string ObtenerDocumentoLiq(string pNumLiquidacion)
        {
            string resultado = string.Empty;
            byte[] Buffer = null;
            OracleBlob blob;

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "GC_K_JRP_DOC_LIQ_PAGADO";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_num_liq", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Blob);

                p2.Direction = ParameterDirection.ReturnValue;
                p1.Value = pNumLiquidacion;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                blob = (OracleBlob)cmd.Parameters["Return_Value"].Value;
                Buffer = new byte[blob.Length];
                blob.Read(Buffer, 0, (int)blob.Length);

                resultado = Convert.ToBase64String(Buffer, 0, Buffer.Length);

                cmd.Dispose();
                cmd = null;
                blob.Close();
                blob.Dispose();
                blob = null;
                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
                resultado = "ERROR " + ex.Message;
            }
            finally
            {
                conexion.Close();
            }
            return resultado;
        }

        /// <summary>
        /// Proceso que valida criterios si procede el pago o no
        /// </summary>
        /// <param name="p_num_sini">Numero de siniestro</param>
        /// <param name="p_cod_cia">Codigo de compania (2=MGT)</param>
        /// <returns></returns>
        public string Core_TW_ValidarLiquidacion(string p_num_sini,
                                                 string p_cod_cia = "2")
        {
            string resultado = string.Empty;

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "ts_k_utils_mgt.f_dev_validacion";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_cod_cia", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("p_num_sini", OracleDbType.Int64);
                OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.Varchar2, 32767);

                p1.Value = p_cod_cia ?? "2";
                p2.Value = p_num_sini;
                retorno.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(retorno);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);

                cmd.ExecuteNonQuery();

                resultado = cmd.Parameters["Return_Value"].Value.ToString();

                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultado;
        }

        /// <summary>
        /// Proceso para realizar el calculo del pago del deducible
        /// </summary>
        /// <param name="p_num_sini"></param>
        /// <param name="p_mca_factura"></param>
        /// <param name="p_num_cuota"></param>
        /// <param name="p_cod_cia"></param>
        /// <returns></returns>
        public DataTable CalcularLiquidacion(string p_num_sini,
                                             string p_mca_factura = "N",
                                             string p_num_cuota = "1",
                                             string p_cod_cia = "2")
        {
            DataTable resultados = new DataTable();

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "ts_k_utils_mgt.f_dev_calculo_dedu";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_cod_cia", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("p_num_sini", OracleDbType.Int64);
                OracleParameter p3 = new OracleParameter("p_mca_factura", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("p_num_cuota", OracleDbType.Varchar2);
                OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.RefCursor);

                p1.Value = p_cod_cia ?? "2";
                p2.Value = p_num_sini;
                p3.Value = p_mca_factura ?? "N";
                p4.Value = p_num_cuota ?? "1";
                retorno.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(retorno);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);

                resultados.Load(cmd.ExecuteReader());

                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
            }
            finally
            {
                conexion.Close();
            }

            return resultados;
        }

        /// <summary>
        /// Proceso para generar el numero de deducible
        /// </summary>
        /// <param name="p_num_sini"></param>
        /// <param name="p_mca_factura"></param>
        /// <param name="p_num_cuota"></param>
        /// <param name="p_cod_cia"></param>
        /// <returns></returns>
        public long CrearLiquidacion(string p_num_sini,
                                     string p_mca_factura = "N",
                                     string p_num_cuota = "1",
                                     string p_cod_cia = "2",
                                     Estructuras.Bitacora_wsTransacLinea Bitacora = null)
        {
            long resultado = 0;
            ConexionBD clConexion = new ConexionBD();
            MySQLLogger mySQLLogger = new MySQLLogger();

            OracleConnection conexion = new OracleConnection();

            #region Bitacora
            if (Bitacora == null)
            {
                Bitacora = new Estructuras.Bitacora_wsTransacLinea();
            }

            Estructuras.Bitacora_DetalleWSTransacLinea Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();
            Detalle_Bitacora.PROCEDURE = "ts_k_utils_mgt.f_dev_genera_dedu";
            Detalle_Bitacora.METODO = "Core_TW_CrearLiquidacion";
            Detalle_Bitacora.XML_IN = new XElement("DATOS",
                                        new XElement("NUM_SINIESTRO", p_num_sini),
                                        new XElement("MCA_FACTURA", p_mca_factura),
                                        new XElement("NUM_CUOTA", p_num_cuota)).ToString();
            Detalle_Bitacora.FECHA_IN = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
            Detalle_Bitacora.REINTEGRO_TARJETA = "0";
            Detalle_Bitacora.COBRO_TARJETA = "0";
            #endregion

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "ts_k_utils_mgt.f_dev_genera_dedu";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_cod_cia", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("p_num_sini", OracleDbType.Int64);
                OracleParameter p3 = new OracleParameter("p_mca_factura", OracleDbType.Varchar2);
                OracleParameter p4 = new OracleParameter("p_num_cuota", OracleDbType.Varchar2);
                OracleParameter retorno = new OracleParameter("Return_Value", OracleDbType.Int64);

                p1.Value = p_cod_cia ?? "2";
                p2.Value = p_num_sini;
                p3.Value = p_mca_factura ?? "N";
                p4.Value = p_num_cuota ?? "1";
                retorno.Direction = ParameterDirection.ReturnValue;

                cmd.Parameters.Add(retorno);
                cmd.Parameters.Add(p1);
                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p3);
                cmd.Parameters.Add(p4);

                cmd.ExecuteNonQuery();

                resultado = Int64.Parse(cmd.Parameters["Return_Value"].Value.ToString());

                Detalle_Bitacora.ERROR = resultado > 0 ? "0" : "1";
                Detalle_Bitacora.PAGAR_RECIBO = resultado > 0 ? "1" : "0";
                Detalle_Bitacora.XML_OUT = new XElement("DATOS", new XElement("NUM_DEDUCIBLE", resultado)).ToString();
                conexion.Close();
            }
            catch (Exception ex)
            {
                conexion.Close();
                Detalle_Bitacora.ERROR = "1";
                Detalle_Bitacora.PAGAR_RECIBO = "0";
                Detalle_Bitacora.MENSAJE = ex.Message.Replace(Environment.NewLine, "");
                Detalle_Bitacora.XML_OUT = new XElement("DATOS", new XElement("ERROR", ex.Message)).ToString();
            }
            finally
            {
                conexion.Close();
                Detalle_Bitacora.ID_TRANSACCION = Bitacora.ID_TRANSACCION;
                Detalle_Bitacora.FECHA_OUT = DateTime.Now.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss");
                Bitacora.Detalle_Bitacora = Detalle_Bitacora;
                mySQLLogger.Insert_DetalleBitacora(Bitacora);
            }

            return resultado;
        }

    }
}
