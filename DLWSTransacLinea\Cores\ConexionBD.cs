﻿using MySqlConnector;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Cores
{
    /// <summary>
    /// Conexiones a BD
    /// </summary>
    public class ConexionBD
    {
        /// <summary>
        /// Conexión al core de ACSEL
        /// </summary>
        /// <returns></returns>
        public OracleConnection abrirConexionOracleAcsel()
        {
            OracleConnection conexion = new OracleConnection();
            string strConexion = ConfigurationManager.ConnectionStrings["ConexionOracleAcsel"].ConnectionString;
            conexion.ConnectionString = strConexion;

            try
            {
                conexion.Open();
                OracleGlobalization info = conexion.GetSessionInfo();
                info.Language = "SPANISH";
                info.DateFormat = "DD/MM/RRRR";
                conexion.SetSessionInfo(info);
            }
            catch (OracleException ex)
            {
                //throw (ex);
            }

            return conexion;
        }

        /// <summary>
        /// Conexión al core de TRONWEB
        /// </summary>
        /// <returns></returns>
        public OracleConnection abrirConexionOracleTRONWEB()
        {
            OracleConnection conexion = new OracleConnection();
            string strConexion = ConfigurationManager.ConnectionStrings["ConexionOracleTRONWEB"].ConnectionString;
            conexion.ConnectionString = strConexion;

            try
            {
                conexion.Open();
            }
            catch (OracleException ex)
            {
                throw (ex);
            }

            return conexion;
        }

        /// <summary>
        /// Conexión a la BD de MySQ (Log)
        /// </summary>
        /// <returns></returns>
        public MySqlConnection abrirConexionMysqlLog()
        {
            MySqlConnection conexion = new MySqlConnection();
            string strConexion = ConfigurationManager.ConnectionStrings["ConexionMySqlLog"].ConnectionString;
            conexion.ConnectionString = strConexion;

            try
            {
                conexion.Open();
            }
            catch (OracleException ex)
            {
                throw (ex);
            }

            return conexion;
        }
    }
}
