﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Structures.POS
{
    public class DEBITO_SAS
    {
        [XmlElement]
        public DATO DATOS;
        [XmlRoot]

        public class DATO
        {
            [XmlElement]
            public string StsPos;
            [XmlElement]
            public string TDCNumber;
            [XmlElement]
            public string Monto;
            [XmlElement]
            public string FecVencTDC;
            [XmlElement]
            public string CodRespuesta;
            [XmlElement]
            public string DescRespuesta;
            [XmlElement]
            public string FechaAuto;
            [XmlElement]
            public string HoraAuto;
            [XmlElement]
            public string NumReferencia;
            [XmlElement]
            public string NumAutorizacion;
            [XmlElement]
            public string IDTransaccion;
            [XmlElement]
            public string IdTerminal;
            [XmlElement]
            public string IDTransaccion_ref;
            [XmlElement]
            public string pCodUsr { get; set; }
        }
    }
}
