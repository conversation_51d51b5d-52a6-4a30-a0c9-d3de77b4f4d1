pipeline {
    agent any
    options {
        timeout(time: 1, unit: 'HOURS') 
    }
    environment{
        SUB_PATH = '\\'
        SLN_NAME = 'WCFTransacLinea.sln'
        PROJECT_NAME = 'WCFTransacLinea'
        DEPLOY_PATH_APP = '\\INTERNAS\\API\\WsTransacLinea'
        CURRENT_DEPLOY_PATH = 'unmapped'
        ENV_DEPLOY = 'unmapped'
        BACKUP_PATH = 'unmapped'
        BACKUP_FILE = 'unmapped'
        BASEPATH_DEPLOY = 'unmapped'
        DEPLOY_PATH = 'unmapped'
    }
    stages {
        stage ('Clean workspace') {
            steps {
                script {
                    def version = new Date().format("yyyyMMdd")
                    PREFIX_VERSION = version + '.'
                    REPO_NAME = determineRepoName()
                    BACKUP_FILE = REPO_NAME + "_" + PREFIX_VERSION+ "_" + env.BUILD_NUMBER+ "_" + ".zip"
                    
                    def isReefBranch = BRANCH_NAME.endsWith("_reef")
                    CURRENT_DEPLOY_PATH = isReefBranch ? DEPLOY_PATH_APP + ".REEF" : DEPLOY_PATH_APP

                    if(BRANCH_NAME == "master" || BRANCH_NAME == "master_reef") {
                        ENV_DEPLOY = "prod"
                        BASEPATH_DEPLOY = env.BASEPATH_PRO_BACKUP_APP
                        BACKUP_PATH = env.BASEPATH_PRO_DEPLOY_APP
                    }
                    else if(BRANCH_NAME == "pre" || BRANCH_NAME == "pre_reef") {
                        ENV_DEPLOY = "pre"
                        BASEPATH_DEPLOY = env.BASEPATH_PRE_DEPLOY_APP
                        BACKUP_PATH = env.BASEPATH_PRE_BACKUP_APP
                    }
                    else if(BRANCH_NAME == "int" || BRANCH_NAME == "int_reef") {
                        ENV_DEPLOY = "int"
                        BASEPATH_DEPLOY = env.BASEPATH_INT_DEPLOY_APP
                        BACKUP_PATH = env.BASEPATH_INT_BACKUP_APP
                    }
                    else if (BRANCH_NAME == "dev" || BRANCH_NAME == "dev_reef") {
                        ENV_DEPLOY = "dev"
                        BASEPATH_DEPLOY = env.BASEPATH_DEV_DEPLOY_APP
                        BACKUP_PATH = env.BASEPATH_DEV_BACKUP_APP
                    }

                    BACKUP_PATH = BACKUP_PATH + CURRENT_DEPLOY_PATH
                    DEPLOY_PATH = BASEPATH_DEPLOY + CURRENT_DEPLOY_PATH
                    
                    echo "SLN_NAME = ${SLN_NAME}"
                    echo "SUB_PATH = ${SUB_PATH}"
                    echo "Version = " + version
                    echo "PREFIX_VERSION = ${env.PREFIX_VERSION}"
                    echo 'REPO_NAME = ' + REPO_NAME
                    echo 'BRANCH_NAME = ' + BRANCH_NAME
                    echo 'BACKUP_FILE = ' + BACKUP_FILE
                    echo 'ENV_DEPLOY = ' + ENV_DEPLOY
                    echo 'BASEPATH_DEPLOY = ' + BASEPATH_DEPLOY
                    echo 'DEPLOY_PATH = ' + DEPLOY_PATH
                    echo 'IS_REEF_BRANCH = ' + isReefBranch
                    echo 'CURRENT_DEPLOY_PATH = ' + CURRENT_DEPLOY_PATH
                }
            }
        }
        stage('Restore packages'){
            steps{
                bat "nuget restore \"${SLN_NAME}\""
            }
        }
        stage('Build') {
            parallel {
                stage('On Release') {
                    steps {
                        bat "msbuild \"${SLN_NAME}\" /p:Configuration=Release"
                    }
                }
                stage('Unit Tests') {
                    when {
                        anyOf {
                            branch 'notyet'
                        }
                    }
                    steps {
                        echo "Pending configuration"
                    }
                }
                stage('Quality Gate') {
                    when {
                        anyOf {
                            branch 'dev'
                        }
                    }
                    steps {
                        script {
                            withSonarQubeEnv('App2SonarQube') {
                                bat "dotnet sonarscanner begin /k:${REPO_NAME}" //d:sonar.branch.name=${BRANCH_NAME}
                                bat "MSBuild.exe \"${SLN_NAME}\" /t:Rebuild"
                                bat "dotnet sonarscanner end"
                            }
                        }
                    }
                }
            }
        }
        stage('Create Publish Files'){
            when {
                anyOf {
                    branch 'master'
                    branch 'master_reef'
                    branch 'pre'
                    branch 'pre_reef'
                    branch 'int'
                    branch 'int_reef'
                    branch 'dev'
                    branch 'dev_reef'
                }
            }
            steps{
                 bat "msbuild \"${SLN_NAME}\" /p:Configuration=Release /p:Configuration=Release /p:OutDir=../published/"
                 bat "dir \"${workspace}${SUB_PATH}\\published\\_PublishedWebsites\\${PROJECT_NAME}\""
                 bat "del \"${workspace}${SUB_PATH}\\published\\_PublishedWebsites\\${PROJECT_NAME}\\Web.config\""
            }
        }
        stage('Save BackUp'){
            when{
                anyOf {
                    branch 'master'
                    branch 'master_reef'
                    branch 'pre'
                    branch 'pre_reef'
                    branch 'int'
                    branch 'int_reef'
                    branch 'dev'
                    branch 'dev_reef'
                }
            }
            steps{
                echo "Valid Deployment and BackUp path"
                bat  "IF exist ${DEPLOY_PATH} ( echo ${DEPLOY_PATH} exists ) ELSE ( mkdir ${DEPLOY_PATH} && echo ${DEPLOY_PATH} created)"
                bat  "IF exist ${BACKUP_PATH} ( echo ${BACKUP_PATH} exists ) ELSE ( mkdir ${BACKUP_PATH} && echo ${BACKUP_PATH} created)"

                echo "Backup Name ${BACKUP_FILE}"
                bat  "powershell Compress-Archive ${DEPLOY_PATH} ${BACKUP_PATH}\\${BACKUP_FILE}"
            }
        }
        stage('Deploy'){
            when {
                anyOf {
                    branch 'master'
                    branch 'master_reef'
                    branch 'pre'
                    branch 'pre_reef'
                    branch 'int'
                    branch 'int_reef'
                    branch 'dev'
                    branch 'dev_reef'
                }
           }
           steps{
               echo "Deploying..."
               script {
                   if (DEPLOY_PATH == "unmapped") {
                       error("Deploy path not found")
                   }
                  
                   jiraSendDeploymentInfo environmentId: ENV_DEPLOY, environmentName: ENV_DEPLOY, environmentType: ENV_DEPLOY, site: env.JIRA_SITE, state: 'in_progress'  
                  
                   echo "Begin deploy Frontend WebUI..."
                   
                   
                   bat "ren ${workspace}\\app_offline_template.htm app_offline_template.htm"

                   def offlineApp = bat (returnStatus: true, script: "robocopy ${workspace} ${DEPLOY_PATH} app_offline_template.htm /R:3 /W:2 /TS /FP /NP /ETA")
                   
                   echo "ROBOCOPY returned ${offlineApp}"
                   
                   if (offlineApp < 0 || offlineApp > 3) {
                        error("Offline App failed")
                   }

                   def status = bat (returnStatus: true, script: "robocopy \"${workspace}${SUB_PATH}\\published\\_PublishedWebsites\\${PROJECT_NAME}\" ${DEPLOY_PATH} /E /COPY:DA /R:3 /W:2 /TS /FP /NP /ETA /XO")
                   echo "ROBOCOPY returned ${status}"
                   if (status < 0 || status > 3) {
                       error("ROBOCOPY failed")
                   }
                  
                   bat "del ${DEPLOY_PATH}\\app_offline_template.htm"
                   
                   echo "Ended deploy Frontend WebUI..."
              }
           }
           post {
               always{
                   jiraSendDeploymentInfo environmentId: ENV_DEPLOY, environmentName: ENV_DEPLOY, environmentType: ENV_DEPLOY, site: env.JIRA_SITE  
               }
           }
        }
    }
    post {
        always {
            script {
                if (env.BRANCH_NAME == 'dev' || env.BRANCH_NAME == 'dev_reef' || env.BRANCH_NAME == 'int' || env.BRANCH_NAME == 'int_reef' || env.BRANCH_NAME == 'pre' || env.BRANCH_NAME == 'pre_reef' || env.BRANCH_NAME == 'master' || env.BRANCH_NAME == 'master_reef') {
                    echo 'Audit Log'

                    bat  "IF exist ${env.BASEPATH_AUDIT_LOG}${CURRENT_DEPLOY_PATH} ( echo ${env.BASEPATH_AUDIT_LOG}${CURRENT_DEPLOY_PATH} exists ) ELSE ( mkdir ${env.BASEPATH_AUDIT_LOG}${CURRENT_DEPLOY_PATH} && echo ${env.BASEPATH_AUDIT_LOG}${CURRENT_DEPLOY_PATH} created)"

                    powershell(script: '''
                        $date_execute = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
                        $path_file = "$env:BASEPATH_AUDIT_LOG$env:CURRENT_DEPLOY_PATH\\$env:BRANCH_NAME.log"

                        echo $path_file
                         
                        if (-not (Test-Path $path_file)) {
                            New-Item -ItemType File -Path $path_file | Out-Null
                        }

                        $message_log = "$env:PROJECT_NAME - $env:BRANCH_NAME - $env:BUILD_NUMBER - $date_execute - $env:BUILD_STATUS - $env:CHANGE_AUTHOR"
                        
                        echo $message_log
                        
                        Add-Content -Path $path_file -Value $message_log
                    ''')
                }
            }

            echo 'Send notification'
            emailext recipientProviders: [developers(), requestor()], subject: '$DEFAULT_SUBJECT', replyTo: '$DEFAULT_REPLYTO', body: '${DEFAULT_CONTENT}'
            echo 'Send info to Jira'
            jiraSendBuildInfo site: env.JIRA_SITE
            cleanWs()
        }
    }
}

String determineRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}