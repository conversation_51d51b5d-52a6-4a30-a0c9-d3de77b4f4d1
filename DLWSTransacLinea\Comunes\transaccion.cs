﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Cores;
using DLWSTransacLinea.Structures;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;


namespace DLWSTransacLinea.Comunes
{
    public class transaccion
    {
        /// <summary>
        /// Proceso para ejecutar transaccion solicitada en el XML
        /// </summary>
        /// <param name="xmlDatos"></param>
        /// <returns></returns>
        /// 

        public Estructuras.Bitacora_wsTransacLinea LogTransaccion(Estructuras.Bitacora_wsTransacLinea log)
        {


            return log;
        }

        /// <summary>
        /// Proceso principal
        /// </summary>
        /// <param name="xmlDatos"></param>
        /// <returns></returns>
        public XElement ejecutaTransaccion(string xmlDatos)
        {
            #region xml|Pruebas
            //xmlDatos = xmlTransaccion.generarXmlTransaccion(generar());
            //xmlDatos = "<datos><encabezado><convenio/><proveedor/><codigoRetorno/><mensajeRetorno/><usuarioOrigen>TRON2000</usuarioOrigen><codigoCajero>TRON2000</codigoCajero><tipoBusqueda>REQUERIMIENTO</tipoBusqueda><tipoTransaccion>CONSULTA</tipoTransaccion></encabezado><identificadores><requerimiento>37677;32823548</requerimiento><idepol/><codpol/><numpol/><numcert/><dvid/><numid/><nit/></identificadores><polizas/><requerimientos/><mediosPago/></datos>";
            #endregion

            XElement resultado = null;

            if (!string.IsNullOrWhiteSpace(xmlDatos))
            {
                Estructuras.Bitacora_wsTransacLinea Bitacora_WcfTransacLinea = new Estructuras.Bitacora_wsTransacLinea();
                Bitacora_WcfTransacLinea.Detalle_Bitacora = new Estructuras.Bitacora_DetalleWSTransacLinea();

                #region Bitacoras
                OperationContext context = OperationContext.Current;
                Bitacora_WcfTransacLinea.XML_IN = xmlDatos;
                #endregion

                try
                {
                    xmlTransaccion._transaccion clXMLSolicitud = new xmlTransaccion._transaccion();
                    Consultas clConsultas = new Consultas();
                    ValidacionModule validacionModule = new ValidacionModule();
                    CalculoModule calculoModule = new CalculoModule();
                    GeneraModule generaModule = new GeneraModule();

                    MySQLLogger logger = new MySQLLogger();

                    var doc = new XmlDocument();
                    string pJson = string.Empty;

                    #region Carga|XML
                    doc.LoadXml(xmlDatos);
                    doc = _deserealizarXML(xmlDatos, "datos", new string[] { "//datos/polizas", "//datos/requerimientos", "//datos/mediosPago" });
                    #endregion

                    #region Convierte|XML-Json
                    pJson = JsonConvert.SerializeXmlNode(doc);
                    #endregion

                    #region Convierte|Json-Estructura
                    clXMLSolicitud = JsonConvert.DeserializeObject<xmlTransaccion._transaccion>(pJson);
                    #endregion

                    #region Bitacora_wsTransacLinea|Detalle
                    Bitacora_WcfTransacLinea.METODO = clXMLSolicitud.datos.encabezado.tipoTransaccion;
                    Bitacora_WcfTransacLinea.USUARIO_ORIGEN = clXMLSolicitud.datos.encabezado.usuarioOrigen;
                    Bitacora_WcfTransacLinea.CODIGO_USUARIO = clXMLSolicitud.datos.encabezado.codigoCajero;
                    Bitacora_WcfTransacLinea.SISTEMA_USO = clXMLSolicitud.datos.encabezado.sistemaUso;
                    Bitacora_WcfTransacLinea.ID_REFERENCIA = clXMLSolicitud.datos.encabezado.idReferencia;

                    if (!string.IsNullOrEmpty(clXMLSolicitud.datos.encabezado.idTransaccion))
                    {
                        Bitacora_WcfTransacLinea.ID_TRANSACCION = Int64.Parse(clXMLSolicitud.datos.encabezado.idTransaccion);
                    }
                    else
                    {
                        Bitacora_WcfTransacLinea.ID_TRANSACCION = -1;
                    }

                    #endregion

                    #region Tipo Transaccion
                    switch (clXMLSolicitud.datos.encabezado.tipoTransaccion)
                    {
                        case "CONSULTA":
                            resultado = clConsultas.ConsultarTransaccion(clXMLSolicitud);
                            break;
                        case "PAGO":
                            if (Bitacora_WcfTransacLinea.ID_TRANSACCION == -1)
                            {
                                Bitacora_WcfTransacLinea = logger.Insert_Bitacora(Bitacora_WcfTransacLinea);
                            }
                            resultado = clConsultas.PagarRequerimientos(clXMLSolicitud, Bitacora_WcfTransacLinea);
                            break;
                        case "REVERSION":
                            if (Bitacora_WcfTransacLinea.ID_TRANSACCION == -1)
                            {
                                Bitacora_WcfTransacLinea = logger.Insert_Bitacora(Bitacora_WcfTransacLinea);
                            }
                            resultado = clConsultas.RemesarRequerimientos(clXMLSolicitud, Bitacora_WcfTransacLinea);
                            break;
                        case "VALIDACION":
                            resultado = validacionModule.ValidarDocumento(clXMLSolicitud);
                            break;
                        case "CALCULO":
                            resultado = calculoModule.CalcularTransaccion(clXMLSolicitud);
                            break;
                        case "GENERAR":
                            if (Bitacora_WcfTransacLinea.ID_TRANSACCION == -1)
                            {
                                Bitacora_WcfTransacLinea = logger.Insert_Bitacora(Bitacora_WcfTransacLinea);
                            }
                            resultado = generaModule.GeneraDocumento(clXMLSolicitud, Bitacora: Bitacora_WcfTransacLinea);
                            break;
                        default:
                            //Tipo de transaccion no definido
                            break;
                    }
                    #endregion
                }
                catch (Exception ex)
                {
                    Logger.LogErrorMessage(ex, "Clase [ejecutaTransaccion]");
                }
            }
            else
            {
                //el parametro viene vacio
            }

            return resultado;
        }
        public XmlDocument _deserealizarXML(string XML, string root, string[] pListPath)
        {
            string vUrl = "http://james.newtonking.com/projects/json";
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(XML);

            #region ROOT
            XmlNode node = doc.SelectSingleNode(root);
            XmlAttribute typeAttr = doc.CreateAttribute("xmlns:json");
            typeAttr.Value = vUrl;
            node.Attributes.Append(typeAttr);
            #endregion

            #region LIST
            foreach (var path in pListPath)
            {
                XmlNode nodeChild = doc.SelectSingleNode(path);
                if (nodeChild.ChildNodes.Count == 1)
                {
                    XmlAttribute typeAttrList = doc.CreateAttribute("json", "Array", vUrl);
                    typeAttrList.Value = "true";
                    nodeChild.FirstChild.Attributes.Append(typeAttrList);
                }
            }
            #endregion

            return doc;
        }
        public string _deserealizarXMLaJson(string xml, string raiz, string[] origenListas)
        {
            string resultado = string.Empty;
            var doc = new XmlDocument();

            doc.LoadXml(xml);

            doc = _deserealizarXML(xml, raiz, origenListas);

            resultado = JsonConvert.SerializeXmlNode(doc);

            return resultado;
        }
    }
}
