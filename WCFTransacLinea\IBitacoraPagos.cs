﻿using DLWSTransacLinea.Comunes;
using DLWSTransacLinea.Structures;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea
{
    [ServiceContract]
    public interface IBitacoraPagos
    {
        /// <summary>
        /// Guarda el encabezado de la bitacora
        /// </summary>
        /// <param name="p_bitacora"></param>
        /// <returns></returns>
        [OperationContract]
        Estructuras.Bitacora_wsTransacLinea GuardaBitacora(Estructuras.Bitacora_wsTransacLinea p_bitacora);

        /// <summary>
        /// Guarda el detalle de la bitacora
        /// </summary>
        /// <param name="p_bitacora"></param>
        /// <returns></returns>
        [OperationContract]
        Estructuras.Bitacora_wsTransacLinea GuardaDetalleBitacora(Estructuras.Bitacora_wsTransacLinea p_bitacora);
        
        /// <summary>
        /// Actualizacion de requerimientos y medios de pago.
        /// </summary>
        /// <param name="pBitacora"></param>
        /// <param name="pRequerimientos"></param>
        /// <param name="pMedios"></param>
        /// <returns></returns>
        [OperationContract]
        Estructuras.Bitacora_wsTransacLinea Update_Bitacora(Estructuras.Bitacora_wsTransacLinea pBitacora,
                                                                   List<xmlTransaccion.requerimiento> pRequerimientos = null,
                                                                   List<xmlTransaccion.medio> pMedios = null);
    }
}
