using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la respuesta del endpoint get_info_recibo
    /// </summary>
    public class InfoReciboStructure
    {
        /// <summary>
        /// Código de la compañía
        /// </summary>
        [JsonProperty("codCia")]
        public int CodCia { get; set; }

        /// <summary>
        /// Número de póliza
        /// </summary>
        [JsonProperty("numPoliza")]
        public string NumPoliza { get; set; }

        /// <summary>
        /// Número de recibo
        /// </summary>
        [JsonProperty("numRecibo")]
        public int NumRecibo { get; set; }

        /// <summary>
        /// Código de moneda
        /// </summary>
        [JsonProperty("codMon")]
        public int CodMon { get; set; }

        /// <summary>
        /// Importe del recibo
        /// </summary>
        [JsonProperty("impRecibo")]
        public decimal ImpRecibo { get; set; }

        /// <summary>
        /// Tipo de situación
        /// </summary>
        [JsonProperty("tipSituacion")]
        public string TipSituacion { get; set; }

        /// <summary>
        /// Fecha de vencimiento del recibo
        /// </summary>
        [JsonProperty("fecVctoRecibo")]
        public string FecVctoRecibo { get; set; }

        /// <summary>
        /// Fecha de situación
        /// </summary>
        [JsonProperty("fecSituacion")]

        public string FecSituacion { get; set; }
        /// <summary>
        /// Tipo de documento del titular
        /// </summary>
        [JsonProperty("tipDocum")]
        public string TipDocum { get; set; }

        /// <summary>
        /// Código de documento del titular
        /// </summary>
        [JsonProperty("codDocum")]
        public string CodDocum { get; set; }

        /// <summary>
        /// Nombre completo del titular
        /// </summary>
        [JsonProperty("nombreCompleto")]
        public string NombreCompleto { get; set; }

        /// <summary>
        /// Dirección de cobro del recibo
        /// </summary>
        [JsonProperty("direcCobro")]
        public string DirecCobro { get; set; }

        /// <summary>
        /// Correo electrónico del titular
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; }

        /// <summary>
        /// Fecha contable (Fecha actualización)
        /// </summary>
        [JsonProperty("fecCtable")]
        public string FechaContable { get; set; }

    }
}
