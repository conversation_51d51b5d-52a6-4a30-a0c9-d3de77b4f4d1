using System;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Cliente API para realizar llamadas a los servicios de Guatemala
    /// </summary>
    public class NwtGtApiClient : BaseApiClient
    {
        /// <summary>
        /// Constructor que inicializa el cliente HTTP con la configuración del web.config
        /// </summary>
        public NwtGtApiClient() : base("ReefApi_Gt_BaseUrl", "ReefApi_Gt_Username", "ReefApi_Gt_Password")
        {
        }

        /// <summary>
        /// Consulta la información del deducible utilizando el endpoint get_info_deducible
        /// </summary>
        /// <param name="numLiq">Número de liquidación</param>
        /// <returns>Información del deducible en formato JSON</returns>
        public async Task<string> GetDeducibleInfoAsync(string numLiq)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_info_deducible?codCia={codCia}&numLiq={numLiq}";

                // Usar el nuevo método con retry automático y limpieza de caché
                return await GetStringWithRetryAsync(url);
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar la información del deducible: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Valida el estado del deducible utilizando el endpoint valida_estado_deducible
        /// </summary>
        /// <param name="numSini">Número de siniestro</param>
        /// <returns>Respuesta de validación en formato JSON</returns>
        public async Task<string> ValidateDeducibleStatusAsync(string numSini)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/valida_estado_deducible?codCia={codCia}&numSini={numSini}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al validar el estado del deducible: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta el monto del deducible utilizando el endpoint get_mto_deducible_fac
        /// </summary>
        /// <param name="mcaFactura">Marca de factura</param>
        /// <param name="numCuota">Número de cuota</param>
        /// <param name="numSini">Número de siniestro</param>
        /// <returns>Información del monto del deducible en formato JSON</returns>
        public async Task<string> GetMontoDeducibleAsync(string mcaFactura, string numCuota, string numSini)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_mto_deducible_fac?codCia={codCia}&mcaFactura={mcaFactura}&numCuota={numCuota}&numSini={numSini}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar el monto del deducible: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los avisos de pago de una póliza grupo utilizando el endpoint get-aviso-pago-poliza-grupo
        /// </summary>
        /// <param name="numContrato">Número de contrato</param>
        /// <param name="numPolizaGrupo">Número de póliza grupo</param>
        /// <returns>Información de los avisos de pago de la póliza grupo en formato JSON</returns>
        public async Task<string> GetAvisoPagoPolizaGrupoAsync(string numContrato, string numPolizaGrupo)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_aviso_pago_poliza_grupo?codCia={codCia}&numPolizaGrupo={numPolizaGrupo}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar los avisos de pago de la póliza grupo: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta los contratos de una póliza grupo utilizando el endpoint get_contratos_poliza_grupo
        /// </summary>
        /// <param name="numPolizaGrupo">Número de póliza grupo</param>
        /// <returns>Información de los contratos de la póliza grupo en formato JSON</returns>
        public async Task<string> GetContratosPolizaGrupoAsync(string numPolizaGrupo)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_contratos_poliza_grupo?codCia={codCia}&numPolizaGrupo={numPolizaGrupo}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar los contratos de la póliza grupo: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta la información de un recibo utilizando el endpoint get_info_recibo
        /// </summary>
        /// <param name="numRecibo">Número de recibo</param>
        /// <returns>Información del recibo en formato JSON</returns>
        public async Task<string> GetInfoReciboAsync(string numRecibo)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_info_recibo?codCia={codCia}&numRecibo={numRecibo}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar la información del recibo: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Consulta el responsable de pago utilizando el endpoint get_responsable_pago
        /// </summary>
        /// <param name="codActTercero">Código de actividad del tercero</param>
        /// <param name="codDocum">Código del documento</param>
        /// <param name="tipDocum">Tipo de documento</param>
        /// <returns>Información del responsable de pago en formato JSON</returns>
        public async Task<string> GetResponsablePagoAsync(string codActTercero, string codDocum, string tipDocum)
        {
            try
            {
                string codCia = Constants.GetCompany();
                string url = $"newtron/api/transacPagos/get_responsable_pago?codActTercero={codActTercero}&codCia={codCia}&codDocum={codDocum}&tipDocum={tipDocum}";

                HttpResponseMessage response = await _httpClient.GetAsync(url);

                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Error al consultar el responsable de pago: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inesperado: {ex.Message}", ex);
            }
        }

    }
}
