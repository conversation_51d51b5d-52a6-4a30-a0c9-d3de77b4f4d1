<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/WcfComunica.Estructuras" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://10.115.14.4/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System" />
  <xs:import schemaLocation="http://10.115.14.4/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd4" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://10.115.14.4/ws_comunica_d/Wcf/enviar_correo.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/System.Text" />
  <xs:complexType name="dato_correo">
    <xs:sequence>
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="adjunto" nillable="true" type="q1:ArrayOfTupleOfstringstringbase64Binary" />
      <xs:element minOccurs="0" name="asunto" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="contenido" nillable="true" type="xs:string" />
      <xs:element xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="copia" nillable="true" type="q2:ArrayOfstring" />
      <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="copia_oculta" nillable="true" type="q3:ArrayOfstring" />
      <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/System.Text" minOccurs="0" name="encode" nillable="true" type="q4:Encoding" />
      <xs:element minOccurs="0" name="html" type="xs:boolean" />
      <xs:element minOccurs="0" name="id_contenido" nillable="true" type="xs:string" />
      <xs:element xmlns:q5="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="para" nillable="true" type="q5:ArrayOfstring" />
      <xs:element xmlns:q6="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="parametro" nillable="true" type="q6:ArrayOfKeyValueOfstringstring" />
      <xs:element minOccurs="0" name="usuario" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="dato_correo" nillable="true" type="tns:dato_correo" />
</xs:schema>