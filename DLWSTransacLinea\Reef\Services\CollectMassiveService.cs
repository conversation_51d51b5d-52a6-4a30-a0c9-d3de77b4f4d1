using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DLWSTransacLinea.Structures;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;
using System.Linq;
using DLWSTransacLinea.Reef.Converters;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para procesar cobranzas masivas en REEF
    /// </summary>
    public class CollectMassiveService
    {
        private readonly NwtTsyApiClient _apiClient;

        public CollectMassiveService()
        {
            _apiClient = new NwtTsyApiClient();
        }

        /// <summary>
        /// Procesa una cobranza masiva de recibos
        /// </summary>
        /// <param name="requerimientos">Lista de requerimientos/recibos a cobrar</param>
        /// <param name="mediosPago">Lista de medios de pago</param>
        /// <param name="encabezado">Información del encabezado de la transacción</param>
        /// <param name="bitacora">Objeto de bitácora para logging</param>
        /// <param name="btcMvmTypVal">Tipo de movimiento de lote</param>
        /// <param name="canMvm">Cancelar movimiento</param>
        /// <param name="pocDat">Fecha de procesamiento</param>
        /// <param name="pgwVal">Gateway de pago</param>
        /// <returns>Resultado del procesamiento de la cobranza masiva</returns>
        public async Task<ResultadoCobranzaMasiva> ProcesarCobranzaMasivaAsync(
            List<xmlTransaccion.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado,
            DLWSTransacLinea.Comunes.Estructuras.Bitacora_wsTransacLinea bitacora,
            int btcMvmTypVal = 10,
            string canMvm = "N",
            long? pocDat = null,
            string pgwVal = "")
        {
            var resultado = new ResultadoCobranzaMasiva();

            try
            {
                // Validaciones básicas
                if (requerimientos == null || requerimientos.Count == 0)
                {
                    resultado.MensajeError = "No se proporcionaron requerimientos para procesar";
                    return resultado;
                }

                if (mediosPago == null || mediosPago.Count == 0)
                {
                    resultado.MensajeError = "No se proporcionaron medios de pago";
                    return resultado;
                }

                // Construir el request para la cobranza masiva
                var request = ConstruirRequestCobranzaMasiva(requerimientos, mediosPago, encabezado);

                // Llamar al endpoint de cobranza masiva
                string respuestaJson = await _apiClient.PostCollectMassiveAsync(
                    request: request,
                    btcMvmTypVal: btcMvmTypVal,
                    canMvm: canMvm,
                    pocDat: pocDat,
                    pgwVal: pgwVal
                );

                // Procesar respuesta
                resultado = ProcesarRespuestaCobranzaMasiva(respuestaJson, requerimientos);

                Console.WriteLine($"Cobranza masiva procesada exitosamente. Recibos procesados: {resultado.RecibosProcessados.Count}");
            }
            catch (Exception ex)
            {
                resultado.Exitoso = false;
                resultado.MensajeError = $"Error general en el procesamiento de cobranza masiva: {ex.Message}";
                Console.WriteLine($"Error general en CollectMassiveService: {ex.Message}");
            }

            return resultado;
        }

        /// <summary>
        /// Construye el request para la cobranza masiva
        /// </summary>
        private CollectMassiveRequest ConstruirRequestCobranzaMasiva(
            List<xmlTransaccion.requerimiento> requerimientos,
            List<xmlTransaccion.medio> mediosPago,
            xmlTransaccion._encabezado encabezado)
        {
            var request = new CollectMassiveRequest();
            var monedaService = new MonedaService();

            var monedas = monedaService.ConsultaMonedas();

            foreach (var requerimiento in requerimientos)
            {
                var itemRecibo = ConstruirItemRecibo(requerimiento, encabezado, monedas);

                request.oRcpMtgHedPT.Add(new ReceiptMassiveHeader
                {
                    chtBnkTypVal = itemRecibo.chtBnkTypVal,
                    cloPymTypVal = itemRecibo.cloPymTypVal,
                    cloStsTypVal = itemRecibo.cloStsTypVal,
                    cmpVal = itemRecibo.cmpVal,
                    cosIdnVal = itemRecibo.cosIdnVal,
                    cosCrnVal = itemRecibo.cosCrnVal,
                    crnVal = itemRecibo.crnVal,
                    cshVal = itemRecibo.cshVal,
                    mtcVal = itemRecibo.mtcVal,
                    obsVal = itemRecibo.obsVal,
                    pgwTxtVal = itemRecibo.pgwTxtVal,
                    pgwVal = itemRecibo.pgwVal,
                    pocDat = itemRecibo.pocDat,
                    prcTrmVal = itemRecibo.prcTrmVal,
                    rcpAmn = itemRecibo.rcpAmn,
                    rcpVal = itemRecibo.rcpVal,
                    sndDat = itemRecibo.sndDat,
                    usrVal = itemRecibo.usrVal
                });

                request.oRcpMtgPT.Add(new ReceiptMassiveItem
                {
                    chtBnkTypVal = itemRecibo.chtBnkTypVal,
                    cloPymTypVal = itemRecibo.cloPymTypVal,
                    cloStsTypVal = itemRecibo.cloStsTypVal,
                    cmpVal = itemRecibo.cmpVal,
                    cosIdnVal = itemRecibo.cosIdnVal,
                    cosCrnVal = itemRecibo.cosCrnVal,
                    crnVal = itemRecibo.crnVal,
                    cshVal = itemRecibo.cshVal,
                    mtcVal = itemRecibo.mtcVal,
                    obsVal = itemRecibo.obsVal,
                    pgwTxtVal = itemRecibo.pgwTxtVal,
                    pgwVal = itemRecibo.pgwVal,
                    pocDat = itemRecibo.pocDat,
                    prcTrmVal = itemRecibo.prcTrmVal,
                    rcpAmn = itemRecibo.rcpAmn,
                    rcpVal = itemRecibo.rcpVal,
                    sndDat = itemRecibo.sndDat,
                    usrVal = itemRecibo.usrVal
                });
            }

            int secuencia = 1;
            foreach (var requerimiento in requerimientos)
            {
                foreach (var medio in mediosPago)
                {
                    var itemMedioPago = ConstruirItemMedioPago(requerimiento, medio, encabezado, secuencia);

                    request.oRcpMtgCosPT.Add(new ReceiptMassiveCost
                    {
                        chtBnkTypVal = itemMedioPago.chtBnkTypVal,
                        cloPymTypVal = itemMedioPago.cloPymTypVal,
                        cloStsTypVal = itemMedioPago.cloStsTypVal,
                        cmpVal = itemMedioPago.cmpVal,
                        cosIdnVal = itemMedioPago.cosIdnVal,
                        cosCrnVal = itemMedioPago.cosCrnVal,
                        crnVal = itemMedioPago.crnVal,
                        cshVal = itemMedioPago.cshVal,
                        mtcVal = itemMedioPago.mtcVal,
                        obsVal = itemMedioPago.obsVal,
                        pgwTxtVal = itemMedioPago.pgwTxtVal,
                        pgwVal = itemMedioPago.pgwVal,
                        pocDat = itemMedioPago.pocDat,
                        prcTrmVal = itemMedioPago.prcTrmVal,
                        rcpAmn = itemMedioPago.rcpAmn,
                        rcpVal = itemMedioPago.rcpVal,
                        smfAcoTypVal = itemMedioPago.smfAcoTypVal,
                        smfAcoVal = itemMedioPago.smfAcoVal,
                        sndDat = itemMedioPago.sndDat,
                        usrVal = itemMedioPago.usrVal,
                        sqnCosIdnVal = secuencia.ToString()
                    });

                    secuencia++;
                }
            }

            return request;
        }

        /// <summary>
        /// Construye un item de recibo para oRcpMtgHedPT y oRcpMtgPT
        /// </summary>
        private ReceiptMassiveHeader ConstruirItemRecibo(
            xmlTransaccion.requerimiento requerimiento,
            xmlTransaccion._encabezado encabezado,
            List<MonedaStructure> monedas)
        {
            return new ReceiptMassiveHeader
            {
                chtBnkTypVal = "C", // Tipo de Movimiento (caja / Banco U Otros)
                cloPymTypVal = "C", // Tipo Cobro Pago
                cloStsTypVal = "4", // Tipo Situación Cobro
                cmpVal = Constants.GetCompany(), // Compañía
                cosIdnVal = 1230003, // Identificador de compensación para el cobro
                cosCrnVal = 4, // Código moneda compensación (GTQ)
                crnVal = 4, // Moneda (GTQ)
                cshVal = encabezado?.codigoCajero ?? "TRON2000", // Cajero
                mtcVal = 2, // Forma en la Que el Tercero Cobra o Paga
                obsVal = "PRUEBA API", // Observaciones
                pgwTxtVal = "PRUEBA API", // Clob recibido de la pasarela de pago
                pgwVal = "", // Pasarela de Pago
                pocDat = Utils.DateTimeToUnixTimeStamp(DateTime.Now), // Fecha en la Que Se Realiza el Proceso Masivo
                prcTrmVal = "0", // Terminación
                rcpAmn = double.Parse(requerimiento.totalRequerimiento), // Total del Recibo
                rcpVal = int.Parse(requerimiento.numeroRequerimiento), // Recibo
                sndDat = Utils.DateTimeToUnixTimeStamp(DateTime.Now), // Fecha Envío
                usrVal = Constants.GetUser() // Usuario
            };
        }

        /// <summary>
        /// Construye un item de medio de pago para oRcpMtgCosPT
        /// </summary>
        private ReceiptMassiveHeader ConstruirItemMedioPago(
            xmlTransaccion.requerimiento requerimiento,
            xmlTransaccion.medio medio,
            xmlTransaccion._encabezado encabezado,
            int secuencia)
        {
            // Obtener número de recibo
            int numeroRecibo = int.TryParse(requerimiento.numeroRequerimiento, out var rcpVal) ? rcpVal : 125384;

            // Construir descripción del medio de pago
            string descripcionMedio = ConstruirDescripcionMedioPago(medio);
            string textoGateway = ConstruirTextoGateway(medio);

            return new ReceiptMassiveHeader
            {
                chtBnkTypVal = "C", // Tipo de Movimiento (caja / Banco U Otros)
                cloPymTypVal = "C", // Tipo Cobro Pago
                cloStsTypVal = "4", // Tipo Situación Cobro
                cmpVal = Constants.GetCompany(), // Compañía
                cosIdnVal = 1230003, // Identificador de compensación para el cobro
                cosCrnVal = ObtenerIdMoneda(medio.codMoneda), // Código moneda compensación
                crnVal = ObtenerIdMoneda(medio.codMoneda), // Moneda
                cshVal = encabezado?.codigoCajero ?? "TRON2000", // Cajero
                mtcVal = 2, // Forma en la Que el Tercero Cobra o Paga
                obsVal = descripcionMedio, // Observaciones específicas del medio
                pgwTxtVal = textoGateway, // Clob recibido de la pasarela de pago
                pgwVal = MapearGatewayPago(medio.tipoPago), // Pasarela de Pago
                pocDat = Utils.DateTimeToUnixTimeStamp(DateTime.Now), // Fecha en la Que Se Realiza el Proceso Masivo
                prcTrmVal = "0", // Terminación
                rcpAmn = double.Parse(medio.monto), // Monto específico del medio de pago
                rcpVal = numeroRecibo, // Recibo
                smfAcoTypVal = ObtenerTipoCuentaSimplificada(medio.tipoPago), // Tipo de Cuenta Simplificada
                smfAcoVal = medio.CUENTA_SIMPLIFICADA ?? ObtenerCuentaSimplificada(medio.tipoPago), // Cuenta Simplificada
                sndDat = Utils.DateTimeToUnixTimeStamp(DateTime.Now), // Fecha Envío
                usrVal = encabezado?.codigoCajero ?? "TRON2000" // Usuario
            };
        }

        /// <summary>
        /// Obtiene el ID de moneda basado en el código
        /// </summary>
        private int ObtenerIdMoneda(string codigoMoneda)
        {
            switch (codigoMoneda?.ToUpper())
            {
                case "GTQ":
                case "Q":
                    return 4; // Quetzal
                case "USD":
                case "$":
                    return 1; // Dólar
                case "EUR":
                    return 2; // Euro
                default:
                    return 4; // Por defecto Quetzal
            }
        }

        /// <summary>
        /// Mapea el tipo de pago a un gateway específico
        /// </summary>
        private string MapearGatewayPago(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "TAR":
                case "TARJETA":
                    return "PAYU";
                case "EFE":
                case "EFECTIVO":
                    return "";
                case "CHE":
                case "CHEQUE":
                    return "CHEQUE";
                case "DEP":
                case "DEPOSITO":
                    return "DEPOSITO";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TRANSFERENCIA";
                default:
                    return "";
            }
        }

        /// <summary>
        /// Obtiene el total del recibo desde la base de datos
        /// </summary>
        private decimal ObtenerTotalRecibo(string numeroRecibo)
        {
            try
            {
                // TODO: Implementar consulta real a la base de datos para obtener el total del recibo
                // Por ahora retornamos un valor de ejemplo
                return 1088.64m;
            }
            catch (Exception ex)
            {
                // En caso de error, retornar valor por defecto
                return 1088.64m;
            }
        }

        /// <summary>
        /// Construye la descripción del medio de pago
        /// </summary>
        private string ConstruirDescripcionMedioPago(xmlTransaccion.medio medio)
        {
            switch (medio.tipoPago?.ToUpper())
            {
                case "EFE":
                    return "PAGO CON EFECTIVO";
                case "CHE":
                    return "PAGO CON CHEQUE";
                case "TAR":
                    return "PAGO CON TARJETA";
                case "TRA":
                    return "PAGO CON TRANSFERENCIA";
                default:
                    return "PAGO CON EFECTIVO"; // Por defecto
            }
        }

        /// <summary>
        /// Construye el texto del gateway basado en el medio de pago
        /// </summary>
        private string ConstruirTextoGateway(xmlTransaccion.medio medio)
        {
            switch (medio.tipoPago?.ToUpper())
            {
                case "EFE":
                    return "PAGO CON EFECTIVO";
                case "CHE":
                    return $"PAGO CON CHEQUE {medio.numeroDocumento ?? "001011"}";
                case "TAR":
                    return $"PAGO CON TARJETA {medio.numeroDocumento ?? "****1234"}";
                case "TRA":
                    return $"PAGO CON TRANSFERENCIA {medio.numeroDocumento ?? "REF001"}";
                default:
                    return "PAGO CON EFECTIVO"; // Por defecto
            }
        }

        /// <summary>
        /// Obtiene el tipo de cuenta simplificada basado en el tipo de pago
        /// </summary>
        private string ObtenerTipoCuentaSimplificada(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "EFE":
                case "EFECTIVO":
                    return "EF";
                case "TAR":
                case "TARJETA":
                    return "TC";
                case "CHE":
                case "CHEQUE":
                    return "CH";
                case "DEP":
                case "DEPOSITO":
                    return "DP";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TR";
                default:
                    return "EF";
            }
        }

        /// <summary>
        /// Obtiene la cuenta simplificada basada en el tipo de pago
        /// </summary>
        private string ObtenerCuentaSimplificada(string tipoPago)
        {
            switch (tipoPago?.ToUpper())
            {
                case "EFE":
                case "EFECTIVO":
                    return "EF01";
                case "TAR":
                case "TARJETA":
                    return "TC01";
                case "CHE":
                case "CHEQUE":
                    return "CH01";
                case "DEP":
                case "DEPOSITO":
                    return "DP01";
                case "TRA":
                case "TRANSFERENCIA":
                    return "TR01";
                default:
                    return "EF01";
            }
        }

        /// <summary>
        /// Procesa la respuesta de la cobranza masiva
        /// </summary>
        private ResultadoCobranzaMasiva ProcesarRespuestaCobranzaMasiva(string respuestaJson, List<xmlTransaccion.requerimiento> requerimientos)
        {
            var resultado = new ResultadoCobranzaMasiva();

            try
            {
                // Intentar deserializar la respuesta
                dynamic respuesta = JsonConvert.DeserializeObject(respuestaJson);

                // Procesar cada requerimiento
                foreach (var requerimiento in requerimientos)
                {
                    var reciboProcessado = new ReciboProcessadoMasivo
                    {
                        NumeroRecibo = requerimiento.numeroRequerimiento
                    };

                    // Extraer información relevante de la respuesta
                    // Nota: La estructura exacta depende de la respuesta real del API
                    if (respuesta?.success == true || respuesta?.codigo?.ToString() == "00")
                    {
                        reciboProcessado.CAE = respuesta?.cae?.ToString() ?? "PENDIENTE";
                        reciboProcessado.IdCobro = respuesta?.idCobro?.ToString() ?? "PENDIENTE";
                        reciboProcessado.NumeroLote = respuesta?.numeroLote?.ToString() ?? "PENDIENTE";
                        reciboProcessado.FechaProcesamiento = DateTime.Now;
                    }
                    else
                    {
                        reciboProcessado.CAE = "PENDIENTE";
                        reciboProcessado.IdCobro = "PENDIENTE";
                        reciboProcessado.NumeroLote = "PENDIENTE";
                        reciboProcessado.MensajeError = respuesta?.mensaje?.ToString() ?? "Error desconocido en el procesamiento";
                        reciboProcessado.FechaProcesamiento = DateTime.Now;
                    }

                    resultado.RecibosProcessados.Add(reciboProcessado);
                }

                // Determinar si el procesamiento fue exitoso
                resultado.Exitoso = resultado.RecibosProcessados.Count > 0 &&
                                   resultado.RecibosProcessados.TrueForAll(r => string.IsNullOrEmpty(r.MensajeError));

                if (!resultado.Exitoso && resultado.RecibosProcessados.Any(r => !string.IsNullOrEmpty(r.MensajeError)))
                {
                    resultado.MensajeError = "Algunos recibos no pudieron ser procesados correctamente";
                }
            }
            catch (Exception ex)
            {
                // Si no se puede parsear la respuesta, marcar todos como error
                resultado.Exitoso = false;
                resultado.MensajeError = $"Error procesando respuesta de cobranza masiva: {ex.Message}";

                foreach (var requerimiento in requerimientos)
                {
                    resultado.RecibosProcessados.Add(new ReciboProcessadoMasivo
                    {
                        NumeroRecibo = requerimiento.numeroRequerimiento,
                        CAE = "PENDIENTE",
                        IdCobro = "PENDIENTE",
                        NumeroLote = "PENDIENTE",
                        MensajeError = "Error procesando respuesta",
                        FechaProcesamiento = DateTime.Now
                    });
                }
            }

            return resultado;
        }
    }
}
