using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con pólizas grupo
    /// </summary>
    public class PolizaGrupoService
    {
        private readonly NwtIsuApiClient _apiClient;
        private readonly NwtGtApiClient _apiClientGt;
        private readonly PolizaGrupoConverter _converter;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public PolizaGrupoService()
        {
            _apiClient = new NwtIsuApiClient();
            _apiClientGt = new NwtGtApiClient();
            _converter = new PolizaGrupoConverter();
        }

        /// <summary>
        /// Consulta los suplementos de una póliza grupo
        /// </summary>
        /// <param name="gppVal">Número de póliza grupo</param>
        /// <returns>Lista de suplementos de la póliza grupo</returns>
        public List<PolizaGrupoStructure> ConsultarSuplementosPolizaGrupo(string gppVal)
        {
            try
            {
                // Llamar a la API para obtener los suplementos de la póliza grupo
                Task<string> task = _apiClient.GetGroupPolicyEndorsementsAsync(gppVal);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a una lista de PolizaGrupoStructure
                return JsonConvert.DeserializeObject<List<PolizaGrupoStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar suplementos de póliza grupo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta los deals de una póliza grupo
        /// </summary>
        /// <param name="gppVal">Número de póliza grupo</param>
        /// <param name="delVal">Valor del deal</param>
        /// <returns>Lista de deals de la póliza grupo</returns>
        public List<DealPolizaGrupoStructure> ConsultarDealsPolizaGrupo(string gppVal, string delVal)
        {
            try
            {
                Task<string> task = _apiClient.GetGroupPolicyDealsByPolicyAsync(gppVal, delVal);
                task.Wait();
                string jsonResponse = task.Result;

                return JsonConvert.DeserializeObject<List<DealPolizaGrupoStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar deals de póliza grupo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta los contratos de una póliza grupo
        /// </summary>
        /// <param name="numPolizaGrupo">Número de póliza grupo</param>
        /// <returns>Lista de contratos de la póliza grupo</returns>
        public List<ContratoPolizaGrupoStructure> ConsultarContratosPolizaGrupo(string numPolizaGrupo)
        {
            try
            {
                // Llamar a la API para obtener los contratos de la póliza grupo
                Task<string> task = _apiClientGt.GetContratosPolizaGrupoAsync(numPolizaGrupo);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a una lista de objetos ContratoPolizaGrupoStructure
                return JsonConvert.DeserializeObject<List<ContratoPolizaGrupoStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar contratos de póliza grupo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Convierte una lista de PolizaGrupoStructure a un DataTable
        /// </summary>
        /// <param name="polizasGrupo"></param>
        /// <param name="dealsPolizaGrupo"></param>
        /// <returns></returns>
        public DataTable ConvertPolizaGrupoToDataTable(List<PolizaGrupoStructure> polizasGrupo, List<DealPolizaGrupoStructure> dealsPolizaGrupo)
        {
            return _converter.ConvertPolizaGrupoToDataTable(polizasGrupo: polizasGrupo, dealsPolizaGrupo: dealsPolizaGrupo);
        }
    }
}
