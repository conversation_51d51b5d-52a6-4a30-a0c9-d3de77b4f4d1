using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la respuesta del endpoint get_responsable_pago
    /// </summary>
    public class ResponsablePagoStructure
    {
        /// <summary>
        /// Código de la compañía
        /// </summary>
        [JsonProperty("codCia")]
        public int CodCia { get; set; }

        /// <summary>
        /// Código de actividad del tercero
        /// </summary>
        [JsonProperty("codActTercero")]
        public int CodActTercero { get; set; }

        /// <summary>
        /// Tipo de documento
        /// </summary>
        [JsonProperty("tipDocum")]
        public string TipDocum { get; set; }

        /// <summary>
        /// Código del documento
        /// </summary>
        [JsonProperty("codDocum")]
        public string CodDocum { get; set; }

        /// <summary>
        /// Nombre completo del responsable
        /// </summary>
        [JsonProperty("nombreCompleto")]
        public string NombreCompleto { get; set; }

        /// <summary>
        /// Dirección de cobro
        /// </summary>
        [JsonProperty("direcCobro")]
        public string DirecCobro { get; set; }

        /// <summary>
        /// Email del responsable
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; }
    }
}
