using System;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura para la respuesta de avisos de pago de póliza grupo
    /// </summary>
    public class AvisoPagoPolizaGrupoStructure
    {
        [JsonProperty("codActTercero")]
        public int CodActTercero { get; set; }

        [JsonProperty("codCia")]
        public int CodCia { get; set; }

        [JsonProperty("codDocum")]
        public string CodDocum { get; set; }

        [JsonProperty("codDocumPago")]
        public string CodDocumPago { get; set; }

        [JsonProperty("codGestor")]
        public int CodGestor { get; set; }

        [JsonProperty("codMon")]
        public int CodMon { get; set; }

        [JsonProperty("fecMvto")]
        public string FecMvto { get; set; }

        [JsonProperty("fecVcto")]
        public string FecVcto { get; set; }

        [JsonProperty("impDocum")]
        public decimal ImpDocum { get; set; }

        [JsonProperty("numContrato")]
        public int NumContrato { get; set; }

        [JsonProperty("numMvto")]
        public int NumMvto { get; set; }

        [JsonProperty("numPolizaGrupo")]
        public string NumPolizaGrupo { get; set; }

        [JsonProperty("tipDocum")]
        public string TipDocum { get; set; }

        [JsonProperty("tipDocumPago")]
        public string TipDocumPago { get; set; }

        [JsonProperty("tipEstado")]
        public string TipEstado { get; set; }

        [JsonProperty("tipGestor")]
        public string TipGestor { get; set; }

        [JsonProperty("valCambio")]
        public decimal ValCambio { get; set; }
    }
}
