using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con recibos
    /// </summary>
    public class ReciboService
    {
        private readonly NwtTsyApiClient _apiClient;
        private readonly NwtGtApiClient _apiClientGt;
        private readonly ReciboConverter _converter;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public ReciboService()
        {
            _apiClient = new NwtTsyApiClient();
            _apiClientGt = new NwtGtApiClient();
            _converter = new ReciboConverter();
        }

        /// <summary>
        /// Recupera la información de un recibo
        /// </summary>
        /// <param name="receiptId"></param>
        /// <returns></returns>
        public ORcpErcC ConsultaInformacionRecibo(string receiptId)
        {
            try
            {
                Task<string> task = _apiClient.GetReceiptCompleteAsync(receiptId: receiptId);
                task.Wait();
                string jsonResponse = task.Result;
                return JsonConvert.DeserializeObject<ORcpErcC>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar la información del recibo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta los recibos de una póliza utilizando el endpoint querybyply
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="aplEnrSqn">Secuencia de aplicación del suplemento (por defecto 0)</param>
        /// <param name="aplVal">Valor de aplicación (por defecto 0)</param>
        /// <param name="enrSqn">Secuencia del suplemento (por defecto 0)</param>
        /// <returns>Lista de recibos de la póliza</returns>
        public List<ReciboStructure> ConsultarRecibosPorPoliza(string policyNumber, int aplEnrSqn = 0, int aplVal = 0, int enrSqn = 0)
        {
            try
            {
                Task<string> task = _apiClient.GetReceiptByPolicyAsync(policyNumber, aplEnrSqn, aplVal, enrSqn);
                task.Wait();
                string jsonResponse = task.Result;

                // Deserializar la respuesta JSON a una lista de recibos
                return JsonConvert.DeserializeObject<List<ReciboStructure>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar recibos por póliza: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta la información de un recibo
        /// </summary>
        /// <param name="numRecibo">Número de recibo</param>
        /// <returns>Información del recibo</returns>
        public InfoReciboStructure ConsultarInformacionRecibo(string numRecibo)
        {
            try
            {
                // Llamar a la API para obtener la información del recibo
                Task<string> task = _apiClientGt.GetInfoReciboAsync(numRecibo);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a un objeto InfoReciboStructure
                return JsonConvert.DeserializeObject<InfoReciboStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar información del recibo: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Conversion de ORcpErcC a DataTable
        /// </summary>
        /// <param name="receiptInformation">Información del recibo</param>
        /// <param name="currenciesList">Listado de monedas</param>
        /// <returns></returns>
        public DataTable ConvertReciboToDataTable(ORcpErcC receiptInformation, List<MonedaStructure> currenciesList)
        {
            // Usar el converter para convertir la información del recibo
            return _converter.ConvertReciboToDataTable(receiptInformation, currenciesList);
        }

    }
}
