using System;
using System.Collections.Generic;
using System.Data;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;

namespace DLWSTransacLinea.Reef.Converters
{
    /// <summary>
    /// Convertidor para avisos de pago de póliza grupo
    /// </summary>
    public class AvisoPagoPolizaGrupoConverter
    {
        /// <summary>
        /// Convierte la lista de avisos de pago de póliza grupo a DataTable
        /// </summary>
        /// <param name="avisosPago">Lista de avisos de pago de póliza grupo</param>
        /// <param name="currenciesList">Lista de monedas</param>
        /// <returns>DataTable con los datos de los avisos de pago</returns>
        public DataTable ConvertAvisosPagoPolizaGrupoToDataTable(List<AvisoPagoPolizaGrupoStructure> avisosPago, List<MonedaStructure> currenciesList)
        {
            try
            {
                DataTable dataTable = new DataTable("AvisosPagoPolizaGrupo");
                
                // Definir las columnas siguiendo el patrón de otros converters
                dataTable.Columns.Add("REQUERIMIENTO", typeof(string));
                dataTable.Columns.Add("POLIZA", typeof(string));
                dataTable.Columns.Add("ID_MONEDA", typeof(string));
                dataTable.Columns.Add("MONEDA", typeof(string));
                dataTable.Columns.Add("TOTAL", typeof(string));
                dataTable.Columns.Add("FECHA_VENCIMIENTO", typeof(string));
                dataTable.Columns.Add("SISTEMA", typeof(string));
                dataTable.Columns.Add("PAGO", typeof(string));
                dataTable.Columns.Add("STSFACT", typeof(string));
                dataTable.Columns.Add("IDEPOL", typeof(string));
                dataTable.Columns.Add("ASIGNA_FACTURA", typeof(string));
                dataTable.Columns.Add("ES_AVISO", typeof(string));
                dataTable.Columns.Add("FECHA_COBRO", typeof(string));
                dataTable.Columns.Add("PAGADOR", typeof(string));
                dataTable.Columns.Add("CODFACT", typeof(string));
                dataTable.Columns.Add("EMAIL", typeof(string));
                dataTable.Columns.Add("DIREC_COBRO", typeof(string));

                if (avisosPago != null && avisosPago.Count > 0)
                {
                    foreach (var aviso in avisosPago)
                    {
                        // Buscar información de la moneda
                        MonedaStructure monedaInformacion = null;
                        if (currenciesList != null)
                        {
                            foreach (var moneda in currenciesList)
                            {
                                if (moneda.oCrnCrnS.crnVal == aviso.CodMon)
                                {
                                    monedaInformacion = moneda;
                                    break;
                                }
                            }
                        }

                        // Convertir fechas
                        DateTime fechaVencimiento = DateTime.MinValue;
                        DateTime fechaMovimiento = DateTime.MinValue;
                        
                        if (DateTime.TryParse(aviso.FecVcto, out fechaVencimiento) == false)
                        {
                            fechaVencimiento = DateTime.Now;
                        }
                        
                        if (DateTime.TryParse(aviso.FecMvto, out fechaMovimiento) == false)
                        {
                            fechaMovimiento = DateTime.Now;
                        }

                        DataRow row = dataTable.NewRow();
                        row["REQUERIMIENTO"] = aviso.CodDocumPago ?? string.Empty;
                        row["POLIZA"] = aviso.NumPolizaGrupo ?? string.Empty;
                        row["ID_MONEDA"] = aviso.CodMon.ToString();
                        row["MONEDA"] = monedaInformacion?.oCrnCrnS?.sdrCrnVal ?? "GTQ";
                        row["TOTAL"] = aviso.ImpDocum.ToString();
                        row["FECHA_VENCIMIENTO"] = fechaVencimiento.ToString("dd/MM/yyyy");
                        row["SISTEMA"] = Constants.Systems.REEF;
                        row["PAGO"] = aviso.NumMvto.ToString();
                        row["STSFACT"] = aviso.TipEstado ?? string.Empty;
                        row["IDEPOL"] = aviso.NumPolizaGrupo ?? string.Empty;
                        row["ASIGNA_FACTURA"] = "S";
                        row["ES_AVISO"] = "S";

                        // Si está cobrado, agregar fecha de cobro
                        if (aviso.TipEstado == "CT")
                        {
                            row["FECHA_COBRO"] = fechaMovimiento.ToString("dd/MM/yyyy");
                        }
                        else
                        {
                            row["FECHA_COBRO"] = string.Empty;
                        }

                        row["PAGADOR"] = "PENDIENTE"; // Información no disponible en el API
                        row["CODFACT"] = aviso.CodDocumPago ?? string.Empty;
                        row["EMAIL"] = "PENDIENTE"; // Información no disponible en el API
                        row["DIREC_COBRO"] = "PENDIENTE"; // Información no disponible en el API

                        dataTable.Rows.Add(row);
                    }
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al convertir avisos de pago de póliza grupo a DataTable: {ex.Message}");
                return new DataTable();
            }
        }
    }
}
