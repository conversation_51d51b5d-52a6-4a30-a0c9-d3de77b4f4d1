﻿using DLWSTransacLinea.Cores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea.Acsel
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "PagoAcsel" in code, svc and config file together.
    // NOTE: In order to launch WCF Test Client for testing this service, please select PagoAcsel.svc or PagoAcsel.svc.cs at the Solution Explorer and start debugging.
    public class PagoAcsel : IPagoAcsel
    {
        public string Pagar(string xmlCobro)
        {
            string respuesta = string.Empty;

            //xmlCobro = "<PAGO><REQUERIMIENTOS><REQUERIMIENTO>25073412</REQUERIMIENTO></REQUERIMIENTOS><TIPO_PAGO><TIPO><CODIGO>TAR</CODIGO><MONEDA>Q</MONEDA><MONTO>30</MONTO><CODENTFINAN>000186</CODENTFINAN><NUMREFDOC>1234probando</NUMREFDOC><NUMAUTORIZA>123456</NUMAUTORIZA></TIPO></TIPO_PAGO></PAGO>";
            AcselDB logger = new AcselDB();

            //respuesta = logger.pagarAcsel(xmlCobro);

            return respuesta;
        }
        public string buscarPoliza(string xmlBusqueda)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.buscarPolizaAcsel(xmlBusqueda);

            return respuesta;
        }
        public string buscarRequerimientos(string xmlBusqueda)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.buscarRequerimientosAcsel(xmlBusqueda);

            return respuesta;
        }
        public string obtenerTiposDePago()
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerTiposDePagoAcsel();

            return respuesta;
        }
        public string obtenerEntidadesFinancieras()
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerEntidadesFinancierasAcsel();

            return respuesta;
        }
        public string obtenerTiposDeTarjeta()
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerTiposDeTarjetaAcsel();

            return respuesta;
        }
        public string cobrarPOS(string xmlPOS)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();
            // xml de pruebas
            // xmlPOS = "<cobroPOS><pCodProd>PLWQ</pCodProd><pNumPol>1</pNumPol><pNumCert>1</pNumCert><pCodEntFinan>000208</pCodEntFinan><pCardNumber>****************</pCardNumber><pMonto>1</pMonto><pFecVencTDC>03/2023</pFecVencTDC><pCodUsr>ACSEL_WEB</pCodUsr></cobroPOS>";

            respuesta = logger.cobrarPOSAcsel(xmlPOS);

            return respuesta;
        }
        public string reversarPOS(string xmlPOS, string pIdTransaccion = null)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            // xmlPOS = "<respuestaPOS><StsPos>AUT</StsPos><TDCNumber>-4566-21**-****-05647-</TDCNumber><Monto>1</Monto><FecVencTDC>2303</FecVencTDC><CodRespuesta>00</CodRespuesta><DescRespuesta>APROBADA</DescRespuesta><FechaAuto>2021-08-05</FechaAuto><HoraAuto>105359</HoraAuto><NumReferencia>90698479</NumReferencia><NumAutorizacion>622842</NumAutorizacion><IDTransaccion>046828</IDTransaccion><IdTerminal>MAPCOB01</IdTerminal></respuestaPOS>";

            respuesta = logger.reversarPOS(xmlPOS, pIdTransaccion);

            return respuesta;
        }

        #region DESCARGAR FACTURAS
        public string obtenerReporteFacturas(string requerimientos)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerReporteFacturasAcsel(requerimientos);

            return respuesta;
        }

        public string obtenerReporteFacturasFel(string requerimientos)
        {
            AcselDB logger = new AcselDB();
            return logger.obtenerReporteFacturasFel(requerimientos);
        }

        #endregion


        #region RECIBO
        public string obtenerInfoRecibo(string pRequerimiento)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerInfoRecibo(pRequerimiento);

            return respuesta;
        }

        public string obtenerDetalleRecibo(string pRequerimiento)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.obtenerDetalleRecibo(pRequerimiento);

            return respuesta;
        }
        #endregion

        /// <summary>
        /// Proceso para asignar el correo electronico de notificacion de una trasaccion
        /// </summary>
        /// <param name="pIdTransaccion"></param>
        /// <param name="pEmail"></param>
        /// <returns></returns>
        public string Transact_setEmail_Notifica(string pIdTransaccion, string pEmail)
        {
            string respuesta = string.Empty;
            AcselDB logger = new AcselDB();

            respuesta = logger.Transact_Set_Email_Noti(pIdTransaccion, pEmail);

            return respuesta;
        }

        /// <summary>
        /// Recupera el requerimiento de acsel equivalente al recibo de tw
        /// </summary>
        /// <param name="pReciboTron"></param>
        /// <returns></returns>
        public Tuple<string, string> get_equiv_fact_acsel_tron(string pReciboTron)
        {
            try
            {
                AcselDB logger = new AcselDB();
                return logger.get_factura_acsel_migrada(pReciboTron);
            }
            catch (Exception)
            {
                return new Tuple<string, string>("", "");
            }

        }
    }
}
