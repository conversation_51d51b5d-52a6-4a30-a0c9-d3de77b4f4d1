using System;
using System.Collections.Generic;

namespace DLWSTransacLinea.Structures
{
    /// <summary>
    /// Resultado del procesamiento de cobranza masiva en REEF
    /// </summary>
    public class ResultadoCobranzaMasiva
    {
        public bool Exitoso { get; set; }
        public string MensajeError { get; set; }
        public List<ReciboProcessadoMasivo> RecibosProcessados { get; set; }

        public ResultadoCobranzaMasiva()
        {
            RecibosProcessados = new List<ReciboProcessadoMasivo>();
        }
    }

    /// <summary>
    /// Información de un recibo procesado en cobranza masiva
    /// </summary>
    public class ReciboProcessadoMasivo
    {
        public string NumeroRecibo { get; set; }
        public string CAE { get; set; }
        public string IdCobro { get; set; }
        public string NumeroLote { get; set; }
        public DateTime FechaProcesamiento { get; set; }
        public string MensajeError { get; set; }
    }
}
