using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con pólizas
    /// </summary>
    public class PolizaService
    {
        private readonly NwtIsuApiClient _apiClient;
        private readonly PolizaConverter _converter;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public PolizaService()
        {
            _apiClient = new NwtIsuApiClient();
            _converter = new PolizaConverter();
        }

        /// <summary>
        /// Consulta la información operativa de una póliza
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="aplVal">Valor de aplicación (por defecto 0)</param>
        /// <returns>Objeto PolizaStructure con la información de la póliza</returns>
        public PolizaStructure ConsultarInformacionPoliza(string policyNumber, string aplVal = "0")
        {
            try
            {
                // Llamar a la API para obtener los datos de la póliza
                Task<string> task = _apiClient.GetPolicyOperativeDataAsync(policyNumber, aplVal);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a un objeto PolizaStructure
                return JsonConvert.DeserializeObject<PolizaStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar información de póliza: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Consulta los suplementos (endorsements) de una póliza
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <returns>Lista de suplementos de la póliza</returns>
        public List<OPlyGniS> ConsultarSuplementosPoliza(string policyNumber)
        {
            try
            {
                // Llamar a la API para obtener los suplementos de la póliza
                Task<string> task = _apiClient.GetPolicySupplementsAsync(policyNumber);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a una lista de OPlyGniS
                return JsonConvert.DeserializeObject<List<OPlyGniS>>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar suplementos de póliza: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Convierte un objeto PolizaStructure a un DataTable
        /// </summary>
        /// <param name="poliza">Objeto PolizaStructure a convertir</param>
        /// <returns>DataTable con la información de la póliza</returns>
        public DataTable ConvertPolizaToDataTable(PolizaStructure poliza)
        {
            return _converter.ConvertPolizaToDataTable(poliza);
        }

        /// <summary>
        /// Consulta la información operativa de la póliza por suplemento usando el endpoint endorsement/operativedata
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="endorsementNumber">Número de suplemento</param>
        /// <returns>Objeto PolizaStructure con la información de la póliza según el suplemento</returns>
        public PolizaStructure ConsultarInformacionPolizaPorSuplemento(string policyNumber, int endorsementNumber)
        {
            try
            {
                // Llamar a la API para obtener los datos de la póliza por suplemento
                Task<string> task = _apiClient.GetPolicyByEndorsementAsync(policyNumber, endorsementNumber);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a un objeto PolizaStructure
                return JsonConvert.DeserializeObject<PolizaStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar información de póliza por suplemento: {ex.Message}");
                return null;
            }
        }
    }
}
