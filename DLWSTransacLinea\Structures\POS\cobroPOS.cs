﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Structures.POS
{
    [XmlRoot]
    public class cobroPOS
    {
        [XmlElement]
        public string pCodProd;
        [XmlElement]
        public string pNumPol;
        [XmlElement]
        public string pNumCert;
        [XmlElement]
        public string pCodEntFinan;
        [XmlElement]
        public string pCardNumber;
        [XmlElement]
        public string pMonto;
        [XmlElement]
        public string pFecVencTDC;
        [XmlElement]
        public string pCodUsr;
        [XmlElement(IsNullable = true)]
        public string pIdTransaccion;
        [XmlElement(IsNullable = true)]
        public string pRecibos;
    }
}
