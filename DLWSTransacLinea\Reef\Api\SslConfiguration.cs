using System;
using System.Configuration;
using System.Net;
using System.Net.Http;

namespace DLWSTransacLinea.Reef.Api
{
    /// <summary>
    /// Configuración SSL global para todas las APIs
    /// </summary>
    public static class SslConfiguration
    {
        private static bool _isConfigured = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Configura las opciones SSL globales para toda la aplicación
        /// </summary>
        public static void ConfigureGlobalSslSettings()
        {
            if (_isConfigured) return;

            lock (_lockObject)
            {
                if (_isConfigured) return;

                try
                {
                    // Configurar protocolos TLS
                    ConfigureTlsProtocols();

                    // Configurar bypass de certificados SSL si está habilitado
                    ConfigureSslCertificateValidation();

                    _isConfigured = true;
                    System.Diagnostics.Debug.WriteLine("Configuración SSL global aplicada correctamente");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error configurando SSL global: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Configura los protocolos TLS habilitados
        /// </summary>
        private static void ConfigureTlsProtocols()
        {
            try
            {
                // Habilitar TLS 1.2 y versiones compatibles para .NET Framework 4.7.2
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                
                System.Diagnostics.Debug.WriteLine($"Protocolos TLS configurados: {ServicePointManager.SecurityProtocol}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error configurando protocolos TLS: {ex.Message}");
            }
        }

        /// <summary>
        /// Configura la validación de certificados SSL para desarrollo
        /// </summary>
        private static void ConfigureSslCertificateValidation()
        {
            try
            {
                // Verificar si el bypass de SSL está habilitado en configuración
                string ignoreSsl = ConfigurationManager.AppSettings["ReefApi_ignore_Ssl"] ?? "N";
                
                if (string.Equals(ignoreSsl, "S", StringComparison.OrdinalIgnoreCase))
                {
                    // SOLO PARA DESARROLLO: Ignorar errores de certificados SSL
                    ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
                    
                    System.Diagnostics.Debug.WriteLine("ADVERTENCIA: Validación de certificados SSL deshabilitada para desarrollo");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Validación de certificados SSL habilitada para producción");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error configurando validación SSL: {ex.Message}");
            }
        }

        /// <summary>
        /// Crea un HttpClientHandler configurado con las opciones SSL apropiadas
        /// </summary>
        /// <returns>HttpClientHandler configurado</returns>
        public static HttpClientHandler CreateConfiguredHandler()
        {
            // Asegurar que la configuración global esté aplicada
            ConfigureGlobalSslSettings();

            var handler = new HttpClientHandler();

            try
            {
                // Verificar si el bypass de SSL está habilitado
                bool ignoreSsl = Constants.IgnoreSslCertificate();
                
                if (ignoreSsl)
                {
                    // SOLO PARA DESARROLLO: Ignorar errores de certificados SSL
                    handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error configurando HttpClientHandler: {ex.Message}");
            }

            return handler;
        }
    }
}
