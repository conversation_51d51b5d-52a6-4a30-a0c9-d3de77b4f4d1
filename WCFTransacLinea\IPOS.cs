﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea
{
    [ServiceContract]
    public interface IPOS
    {
        [OperationContract]
        string cobrar(string xmlPOS);

        [OperationContract]
        string reversar(string xmlPOS, string pIdTransaccion = null);
    }
}
