﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace DLWSTransacLinea.Structures.POS
{
    [XmlRoot]
    public class respuestaPOS
    {
        [XmlElement]
        public string Sts<PERSON><PERSON> { get; set; }
        [XmlElement]
        public string <PERSON><PERSON><PERSON><PERSON>ber { get; set; }
        [XmlElement]
        public string Monto { get; set; }
        [XmlElement]
        public string FecVencTDC { get; set; }
        [XmlElement]
        public string CodRespuesta { get; set; }
        [XmlElement]
        public string DescRespuesta { get; set; }
        [XmlElement]
        public string Fecha<PERSON>uto { get; set; }
        [XmlElement]
        public string <PERSON>ra<PERSON><PERSON> { get; set; }
        [XmlElement]
        public string NumReferencia { get; set; }
        [XmlElement]
        public string NumAutorizacion { get; set; }
        [XmlElement]
        public string IDTransaccion { get; set; }
        [XmlElement]
        public string IdTerminal { get; set; }
        [XmlElement]
        public string pCodUsr { get; set; }
    }
}
