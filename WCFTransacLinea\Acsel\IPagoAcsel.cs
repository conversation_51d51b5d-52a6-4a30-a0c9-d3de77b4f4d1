﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace WCFTransacLinea.Acsel
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the interface name "IPagoAcsel" in both code and config file together.
    [ServiceContract]
    public interface IPagoAcsel
    {
        [OperationContract]
        string Pagar(string xmlCobro);
        [OperationContract]
        string buscar<PERSON>oliza(string xmlBusqueda);
        [OperationContract]
        string buscarRequerimientos(string xmlBusqueda);
        [OperationContract]
        string obtenerTiposDePago();
        [OperationContract]
        string obtenerEntidadesFinancieras();
        [OperationContract]
        string obtenerTiposDeTarjeta();
        [OperationContract]
        string cobrarPOS(string xmlPOS);
        [OperationContract]
        string reversarPOS(string xmlPOS, string pIdTransaccion = null);
        [OperationContract]
        string obtenerReporteFacturas(string requerimientos);
        [OperationContract]
        string obtenerInfoRecibo(string pRequerimiento);
        [OperationContract]
        string obtenerDetalleRecibo(string pRequerimiento);
        [OperationContract]
        string obtenerReporteFacturasFel(string requerimientos);
        [OperationContract]
        string Transact_setEmail_Notifica(string pIdTransaccion, string pEmail);

        [OperationContract]
        Tuple<string, string> get_equiv_fact_acsel_tron(string pReciboTron);
    }
}
