﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.Models
{
    /// <summary>
    /// Estructura principal que encapsula la información de una moneda utilizada en transacciones.
    /// </summary>
    public class MonedaStructure
    {
        /// <summary>
        /// Objeto con los detalles específicos de la moneda, como código, nombre y decimales.
        /// </summary>
        public OCrnCrnS oCrnCrnS { get; set; }
    }

    /// <summary>
    /// Detalle de la moneda según catálogo oficial.
    /// Ejemplos:
    /// - crnVal = 1, sdrCrnVal = "PAB", crnNam = "BALBOA"
    /// - crnVal = 2, sdrCrnVal = "USD", crnNam = "DÓLAR ESTADOUNIDENSE"
    /// - crnVal = 3, sdrCrnVal = "EUR", crnNam = "EURO"
    /// - crnVal = 4, sdrCrnVal = "GTQ", crnNam = "QUETZAL"
    /// - crnVal = 10, sdrCrnVal = "CHF", crnNam = "FRANCO SUIZO"
    /// - crnVal = 99, sdrCrnVal = "GEN", crnNam = "MONEDA GENERICA"
    /// </summary>
    public class OCrnCrnS
    {
        /// <summary>
        /// Código numérico de la moneda (único por tipo).
        /// </summary>
        public int crnVal { get; set; }

        /// <summary>
        /// Código alfanumérico de la moneda (por ejemplo, "USD", "GTQ", "EUR").
        /// </summary>
        public string sdrCrnVal { get; set; }

        /// <summary>
        /// Nombre oficial de la moneda (por ejemplo, "DÓLAR ESTADOUNIDENSE", "QUETZAL").
        /// </summary>
        public string crnNam { get; set; }

        /// <summary>
        /// Indicador de uso regional. Generalmente "S" para monedas válidas en la región.
        /// </summary>
        public string reaCrn { get; set; }

        /// <summary>
        /// Cantidad de decimales permitidos para la moneda (por ejemplo, 2 para centavos).
        /// </summary>
        public int dclVal { get; set; }
    }
}