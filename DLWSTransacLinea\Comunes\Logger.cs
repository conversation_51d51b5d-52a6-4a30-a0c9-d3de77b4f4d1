﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Comunes
{
    public class Logger
    {
        public static void LogErrorMessage(Exception e, string pId)
        {
            try
            {
                string Directory = ConfigurationManager.AppSettings["directory"] ?? "C:/temp/WSTransacLinea/";

                using (StreamWriter w = File.AppendText(Directory + "/log_" + DateTime.Now.ToString("yyyyMMdd") + ".txt"))
                {
                    Save(pId + "\n" + e.ToString(), w);
                    w.<PERSON>();
                }
            }
            catch (Exception ex) { }
        }
        private static void Save(String logMessage, TextWriter w)
        {
            w.Write("\r\nLog : ");
            w.WriteLine("{0} {1}", DateTime.Now.ToLongTimeString(), DateTime.Now.ToLongDateString());
            w.WriteLine("{0}", logMessage);
            w.WriteLine("-------------------------------");
            w.Flush();
        }
    }
}
