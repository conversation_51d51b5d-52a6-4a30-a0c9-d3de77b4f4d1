using System;
using System.Configuration;
using System.Net;
using System.Web;

namespace WCFTransacLinea
{
    /// <summary>
    /// Clase Global para configuración de la aplicación
    /// </summary>
    public class Global : HttpApplication
    {
        /// <summary>
        /// Método que se ejecuta al iniciar la aplicación
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Application_Start(object sender, EventArgs e)
        {
            // Configurar SSL y TLS de forma global para toda la aplicación
            DLWSTransacLinea.Reef.Api.SslConfiguration.ConfigureGlobalSslSettings();
        }

        /// <summary>
        /// Método que se ejecuta al finalizar la aplicación
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Application_End(object sender, EventArgs e)
        {
            // Limpiar configuraciones si es necesario
        }

        /// <summary>
        /// Método que se ejecuta cuando ocurre un error no manejado
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Application_Error(object sender, EventArgs e)
        {
            // Manejo de errores globales si es necesario
            Exception ex = Server.GetLastError();
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"Error global de aplicación: {ex.Message}");
            }
        }
    }
}
