﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <!--******************************************TRONWEB**************************************************-->
    <!--BASE DE DATOS TRONWEB DESARROLLO-->
    <add name="ConexionOracleTRONWEB" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1523)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = DESGT)));User Id=********;Password=********;" providerName="Oracle.DataAccess" />
    <!--BASE DE DATOS TRONWEB INT-->
    <!--<add name="ConexionOracleTRONWEB" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1542)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = INT)));User Id=movil;Password=**************;" providerName="Oracle.DataAccess"/>-->
    <!--BASE DE DATOS TRONWEB OF0-->
    <!--<add name="ConexionOracleTRONWEB" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1543)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = OF0)));User Id=movil;Password=**************;" providerName="Oracle.DataAccess"/>-->
    <!--BASE DE DATOS TRONWEB PRE-->
    <!--<add name="ConexionOracleTRONWEB" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1544)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = PREPROD)));User Id=movil;Password=**************;" providerName="Oracle.DataAccess"/>-->
    <!--BASE DE DATOS TRONWEB OFICINA PRODUCCION-->
    <!--<add name="ConexionOracleTRONWEB" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = OF0)));User Id=movil;Password=**************;" providerName="Oracle.DataAccess" />-->
    <!---->
    <!--******************************************ACSEL**************************************************-->
    <!--BASE DE DATOS DESARROLLO-->
    <add name="ConexionOracleAcsel" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1521)))(CONNECT_dATA=(SERVER=DEDICATED)(SERVICE_NAME = ACSEL)));User Id=acsel_web;Password=**********;" providerName="Oracle.DataAccess" />
    <!--BASE DE DATOS PREPRODUCCION-->
    <!--<add name="ConexionOracleAcsel" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1521)))(CONNECT_dATA=(SERVER=DEDICATED)(SERVICE_NAME = ppdacsel)));User Id=acsel_web;Password=**********;" providerName="Oracle.DataAccess"/>-->
    <!--BASE DE DATOS PRODUCCION-->
    <!--<add name="ConexionOracleAcsel" connectionString="Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=2126)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME = acsel)));User Id=acsel_web;Password=**********;" providerName="Oracle.DataAccess" />-->
    <!---->
    <!--******************************************MYSQL**************************************************-->
    <!--BASE DE DATOS MYSQL DESALLO -->
    <add name="ConexionMySqlLog" connectionString="Data Source=************2;port=3306;Initial Catalog=bitacora_wstransaclinea;User Id=PBCotWebAut;password=***********;SslMode=Preferred;TLSversion=TLSv1.2;" />
    <!--BASE DE DATOS MYSQL PRODUCCION -->
    <!--<add name="ConexionMySqlLog" connectionString="Data Source=***********;port=3306;Initial Catalog=bitacora_wstransaclinea_d;User Id=PBCotWebAut;password=***********" />-->
  </connectionStrings>
  <appSettings>
    <add key="aspnet:UseTaskFriendlySynchronizationContext" value="true" />
    <!--REEF DESA C.A.-->
    <add key="ReefApi_CompanyValue" value="2" />
    <add key="ReefApi_Language" value="ES" />
    <add key="ReefApi_User" value="FJBRAUZ" />
    <!---->
    <add key="ReefApi_ignore_Ssl" value="S" />
    <!-- ######################### AMBIENTE - DESARROLLO ######################### -->
    <!--EMISION-->
    <add key="ReefApi_Isu_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_isu_api_be-web/" />
    <add key="ReefApi_Isu_Username" value="APITRON" />
    <add key="ReefApi_Isu_Password" value="Mapfre2019" />
    <!--TESORERIA-->
    <add key="ReefApi_Tsy_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_tsy_api_be-web/" />
    <add key="ReefApi_Tsy_Username" value="APITRON" />
    <add key="ReefApi_Tsy_Password" value="Mapfre2019" />
    <!--COMUNES-->
    <add key="ReefApi_Cmn_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_cmn_api_be-web/" />
    <add key="ReefApi_Cmn_Username" value="APITRON" />
    <add key="ReefApi_Cmn_Password" value="Mapfre2019" />
    <!--API GT-->
    <add key="ReefApi_Gt_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_api_gt_be-web/" />
    <add key="ReefApi_Gt_Username" value="APITRON" />
    <add key="ReefApi_Gt_Password" value="Mapfre2019" />
    <!---->
    <!-- ######################### AMBIENTE - IC ######################### -->
    <!--EMISION-->
    <!--<add key="ReefApi_Isu_BaseUrl" value="https://trnic.desa.mapfre.net/nwt_isu_api_be-web/" />-->
    <!--<add key="ReefApi_Isu_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Isu_Password" value="Mapfre2019" />-->
    <!--TESORERIA-->
    <!--<add key="ReefApi_Tsy_BaseUrl" value="https://trnic.desa.mapfre.net/nwt_tsy_api_be-web/" />-->
    <!--<add key="ReefApi_Tsy_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Tsy_Password" value="Mapfre2019" />-->
    <!--COMUNES-->
    <!--<add key="ReefApi_Cmn_BaseUrl" value="https://trnic.desa.mapfre.net/nwt_cmn_api_be-web/" />-->
    <!--<add key="ReefApi_Cmn_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Cmn_Password" value="Mapfre2019" />-->
    <!--API GT-->
    <!--<add key="ReefApi_Gt_BaseUrl" value="https://trnic.desa.mapfre.net/nwt_api_gt_be-web/" />-->
    <!--<add key="ReefApi_Gt_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Gt_Password" value="Mapfre2019" />-->
    <!---->
    <!-- ######################### AMBIENTE - INT ######################### -->
    <!--EMISION-->
    <!--<add key="ReefApi_Isu_BaseUrl" value="https://tron-int-ca.reef.mapfre.net/nwt_isu_api_be-web/" />-->
    <!--<add key="ReefApi_Isu_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Isu_Password" value="Mapfre2019" />-->
    <!--TESORERIA-->
    <!--<add key="ReefApi_Tsy_BaseUrl" value="https://tron-int-ca.reef.mapfre.net/nwt_tsy_api_be-web/" />-->
    <!--<add key="ReefApi_Tsy_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Tsy_Password" value="Mapfre2019" />-->
    <!--COMUNES-->
    <!--<add key="ReefApi_Cmn_BaseUrl" value="https://tron-int-ca.reef.mapfre.net/nwt_cmn_api_be-web/" />-->
    <!--<add key="ReefApi_Cmn_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Cmn_Password" value="Mapfre2019" />-->
    <!--API GT-->
    <!--<add key="ReefApi_Gt_BaseUrl" value="https://tron-int-ca.reef.mapfre.net/nwt_api_gt_be-web/" />-->
    <!--<add key="ReefApi_Gt_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Gt_Password" value="Mapfre2019" />-->
    <!---->
    <!-- ######################### AMBIENTE - PRE ######################### -->
    <!--EMISION-->
    <!--<add key="ReefApi_Isu_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_isu_api_be-web/" />-->
    <!--<add key="ReefApi_Isu_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Isu_Password" value="Mapfre2019" />-->
    <!--TESORERIA-->
    <!--<add key="ReefApi_Tsy_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_tsy_api_be-web/" />-->
    <!--<add key="ReefApi_Tsy_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Tsy_Password" value="Mapfre2019" />-->
    <!--COMUNES-->
    <!--<add key="ReefApi_Cmn_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_cmn_api_be-web/" />-->
    <!--<add key="ReefApi_Cmn_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Cmn_Password" value="Mapfre2019" />-->
    <!--API GT-->
    <!--<add key="ReefApi_Gt_BaseUrl" value="https://tron-pre-ca.reef.mapfre.net/nwt_api_gt_be-web/" />-->
    <!--<add key="ReefApi_Gt_Username" value="APITRON" />-->
    <!--<add key="ReefApi_Gt_Password" value="Mapfre2019" />-->
    <!---->
    <!--CONFIGURACION DE CACHE CLEAR-->
    <add key="CacheClear_MaxRetries" value="1" />
    <add key="CacheClear_RetryDelay" value="1000" />
  </appSettings>
  <system.web>
    <compilation targetFramework="4.7.2" debug="true" />
    <httpRuntime targetFramework="4.5" />
    <customErrors mode="Off" />
  </system.web>
  <system.net>
    <settings>
      <!-- Configuración de red para desarrollo -->
    </settings>
  </system.net>
  <system.serviceModel>
    <client>
      <endpoint address="http://localhost/ws_comunica/Wcf/enviar_correo.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ienviar_correo" contract="WSComunica.Ienviar_correo" name="BasicHttpBinding_Ienviar_correo" />
    </client>
    <bindings>
      <basicHttpBinding>
        <binding sendTimeout="00:10:00" maxBufferPoolSize="2147483647" maxBufferSize="2147483647" maxReceivedMessageSize="2147483647" messageEncoding="Text">
          <readerQuotas maxDepth="2000000" maxStringContentLength="2147483647" maxArrayLength="2147483647" maxBytesPerRead="2147483647" maxNameTableCharCount="2147483647" />
        </binding>
        <binding name="BasicHttpBinding_Ienviar_correo" />
        <binding name="BasicHttpsBinding_Ienviar_correo">
          <security mode="Transport" />
        </binding>
      </basicHttpBinding>
    </bindings>
    <behaviors>
      <serviceBehaviors>
        <behavior>
          <serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
          <serviceDebug includeExceptionDetailInFaults="true" />
        </behavior>
      </serviceBehaviors>
    </behaviors>
    <protocolMapping>
      <add binding="basicHttpsBinding" scheme="https" />
    </protocolMapping>
    <serviceHostingEnvironment aspNetCompatibilityEnabled="true" multipleSiteBindingsEnabled="true" minFreeMemoryPercentageToActivateService="2" />
  </system.serviceModel>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true" />
    <directoryBrowse enabled="true" />
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
