using System;
using System.Threading.Tasks;
using DLWSTransacLinea.Reef.Api;
using DLWSTransacLinea.Reef.Models;
using Newtonsoft.Json;

namespace DLWSTransacLinea.Reef.Services
{
    /// <summary>
    /// Servicio para gestionar operaciones relacionadas con responsables de pago
    /// </summary>
    public class TerceroService
    {
        private readonly NwtGtApiClient _apiClient;

        /// <summary>
        /// Constructor que inicializa las dependencias
        /// </summary>
        public TerceroService()
        {
            _apiClient = new NwtGtApiClient();
        }

        /// <summary>
        /// Consulta el responsable de pago
        /// </summary>
        /// <param name="codActTercero">Código de actividad del tercero</param>
        /// <param name="codDocum">Código del documento</param>
        /// <param name="tipDocum">Tipo de documento</param>
        /// <returns>Información del responsable de pago</returns>
        public ResponsablePagoStructure ConsultarResponsablePago(string codActTercero, string codDocum, string tipDocum)
        {
            try
            {
                // Llamar a la API para obtener la información del responsable de pago
                Task<string> task = _apiClient.GetResponsablePagoAsync(codActTercero, codDocum, tipDocum);
                task.Wait();
                string jsonResponse = task.Result;

                // Convertir la respuesta JSON a un objeto ResponsablePagoStructure
                return JsonConvert.DeserializeObject<ResponsablePagoStructure>(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al consultar responsable de pago: {ex.Message}");
                return null;
            }
        }

    }
}
