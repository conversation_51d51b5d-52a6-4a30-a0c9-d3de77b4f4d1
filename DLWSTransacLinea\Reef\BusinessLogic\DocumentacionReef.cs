﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.BusinessLogic
{
    public class DocumentacionReef
    {

        public string obtenerDocumentoLiq(string pNumLiquidacion)
        {
            return string.Empty;
            /*string resultado = string.Empty;
            byte[] Buffer = null;
            OracleBlob blob;

            OracleConnection conexion = new OracleConnection();
            ConexionBD clConexion = new ConexionBD();

            try
            {
                OracleCommand cmd = new OracleCommand();
                conexion = clConexion.abrirConexionOracleTRONWEB();
                cmd.Connection = conexion;
                cmd.CommandText = "GC_K_JRP_DOC_LIQ_PAGADO";
                cmd.CommandType = CommandType.StoredProcedure;
                OracleParameter p1 = new OracleParameter("p_num_liq", OracleDbType.Int64);
                OracleParameter p2 = new OracleParameter("Return_Value", OracleDbType.Blob);

                p2.Direction = ParameterDirection.ReturnValue;
                p1.Value = pNumLiquidacion;

                cmd.Parameters.Add(p2);
                cmd.Parameters.Add(p1);

                cmd.ExecuteNonQuery();

                blob = (OracleBlob)cmd.Parameters["Return_Value"].Value;
                Buffer = new byte[blob.Length];
                blob.Read(Buffer, 0, (int)blob.Length);

                resultado = Convert.ToBase64String(Buffer, 0, Buffer.Length);

                cmd.Dispose();
                cmd = null;
                blob.Close();
                blob.Dispose();
                blob = null;
                conexion.Close();
            }
            catch (OracleException ex)
            {
                conexion.Close();
                resultado = "ERROR " + ex.Message;
            }
            finally
            {
                conexion.Close();
            }
            return resultado;*/
        }
    }
}
