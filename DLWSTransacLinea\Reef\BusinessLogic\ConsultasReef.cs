﻿using DLWSTransacLinea.Reef.Converters;
using DLWSTransacLinea.Reef.Services;
using DLWSTransacLinea.Reef.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DLWSTransacLinea.Reef.BusinessLogic
{
    /// <summary>
    /// Clase de lógica de negocio para consultas relacionadas con pólizas, recibos y avisos de pago en el sistema Reef
    /// </summary>
    public class ConsultasReef
    {
        #region Constants

        /// <summary>
        /// Constante para identificar avisos de pago tipo AB
        /// </summary>
        private const string AVISO_PAGO_TYPE_AB = "AB";

        #endregion

        #region Private Fields

        /// <summary>
        /// Servicio para operaciones con pólizas
        /// </summary>
        private readonly PolizaService _polizaService;

        /// <summary>
        /// Servicio para operaciones con recibos
        /// </summary>
        private readonly ReciboService _reciboService;

        /// <summary>
        /// Servicio para operaciones con avisos de pago
        /// </summary>
        private readonly AvisoPagoService _avisoPagoService;

        /// <summary>
        /// Servicio para operaciones con monedas
        /// </summary>
        private readonly MonedaService _monedaService;

        /// <summary>
        /// Servicio para operaciones con pólizas grupo
        /// </summary>
        private readonly PolizaGrupoService _polizaGrupoService;

        #endregion

        #region Constructor

        /// <summary>
        /// Constructor que inicializa todos los servicios necesarios
        /// </summary>
        public ConsultasReef()
        {
            _polizaService = new PolizaService();
            _reciboService = new ReciboService();
            _avisoPagoService = new AvisoPagoService();
            _monedaService = new MonedaService();
            _polizaGrupoService = new PolizaGrupoService();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Obtiene las vigencias de una póliza, manejando tanto pólizas individuales como pólizas grupo
        /// </summary>
        /// <param name="policyNumber">Número de póliza a consultar</param>
        /// <returns>DataTable con la información de las vigencias de la póliza</returns>
        /// <exception cref="ArgumentException">Se lanza cuando el número de póliza es nulo o vacío</exception>
        public DataTable ObtenerVigenciasPoliza(string policyNumber)
        {
            // Validación de parámetros de entrada
            if (string.IsNullOrWhiteSpace(policyNumber))
            {
                throw new ArgumentException("El número de póliza no puede ser nulo o vacío", nameof(policyNumber));
            }

            try
            {
                // Consultar suplementos de la póliza
                var suplementosPoliza = _polizaService.ConsultarSuplementosPoliza(policyNumber);
                if (suplementosPoliza == null || !suplementosPoliza.Any())
                {
                    PolizaGrupoService polizaGrupoService = new PolizaGrupoService();

                    var contratosPolizaGrupo = polizaGrupoService.ConsultarContratosPolizaGrupo(numPolizaGrupo: policyNumber);

                    if (contratosPolizaGrupo != null)
                    {
                        var contratoPolizaGrupo = contratosPolizaGrupo.Where(x => x.CantAviso >= 0).FirstOrDefault() ?? contratosPolizaGrupo.FirstOrDefault();

                        OPlyGniS oPlyGniS = new OPlyGniS();
                        oPlyGniS.gppVal = contratoPolizaGrupo.NumPoliza;
                        oPlyGniS.delVal = contratoPolizaGrupo.NumContrato.ToString();

                        return ProcesarPolizaGrupo(oPlyGniS);
                    }
                }

                // Verificar si es una póliza grupo
                var polizaGrupo = IdentificarPolizaGrupo(suplementosPoliza);
                if (polizaGrupo != null)
                {
                    var dtPolizaGrupo = ProcesarPolizaGrupo(polizaGrupo);
                    if (dtPolizaGrupo != null && dtPolizaGrupo.Rows.Count > 0)
                    {
                        return dtPolizaGrupo;
                    }
                }

                // Procesar como póliza individual
                return ProcesarPolizaIndividual(policyNumber, suplementosPoliza);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener vigencias de póliza {policyNumber}: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// Recupera la información de recibo o aviso de pago según el tipo de documento especificado
        /// </summary>
        /// <param name="pymDcmVal">Código de documento de pago</param>
        /// <param name="pymDcmTypVal">Tipo de documento de pago (opcional). Si es "AB" busca aviso de pago, si es otro valor busca recibo, si es null busca ambos</param>
        /// <returns>DataTable con la información del recibo y/o aviso de pago encontrado</returns>
        /// <exception cref="ArgumentException">Se lanza cuando el código de documento es nulo o vacío</exception>
        private DataTable ObtenerReciboAviso(string pymDcmVal, string pymDcmTypVal = null)
        {
            // Validación de parámetros de entrada
            if (string.IsNullOrWhiteSpace(pymDcmVal))
            {
                throw new ArgumentException("El código de documento de pago no puede ser nulo o vacío", nameof(pymDcmVal));
            }

            try
            {
                var dtInformacion = new DataTable();

                // Obtener lista de monedas una sola vez para reutilizar
                var monedas = ObtenerMonedas();
                if (monedas == null)
                {
                    Console.WriteLine("No se pudieron obtener las monedas del sistema");
                    return dtInformacion;
                }

                if (!string.IsNullOrEmpty(pymDcmTypVal))
                {
                    // Buscar según tipo específico
                    if (pymDcmTypVal == AVISO_PAGO_TYPE_AB)
                    {
                        var dtAviso = ProcesarAvisoPago(pymDcmVal, monedas);
                        if (dtAviso != null && dtAviso.Rows.Count > 0)
                        {
                            dtInformacion = dtAviso;
                        }
                    }
                    else
                    {
                        var dtRecibo = ProcesarRecibo(pymDcmVal, monedas);
                        if (dtRecibo != null && dtRecibo.Rows.Count > 0)
                        {
                            dtInformacion = dtRecibo;
                        }
                    }
                }
                else
                {
                    // Buscar tanto recibo como aviso de pago
                    var dtRecibo = ProcesarRecibo(pymDcmVal, monedas);
                    var dtAviso = ProcesarAvisoPago(pymDcmVal, monedas);

                    if (dtRecibo != null && dtRecibo.Rows.Count > 0)
                    {
                        dtInformacion = dtRecibo.Copy();
                    }

                    if (dtAviso != null && dtAviso.Rows.Count > 0)
                    {
                        MergeDataTableSafely(dtInformacion, dtAviso);
                    }
                }

                return dtInformacion;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener información de recibo/aviso para documento {pymDcmVal}: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// Recupera la información de documentos de pago (recibos y avisos de pago) según los criterios especificados
        /// </summary>
        /// <param name="policyNumber">Número de póliza (opcional)</param>
        /// <param name="initialValidity">Fecha inicio de vigencia (opcional, no implementado)</param>
        /// <param name="isGroupPolicy">Indicador si es póliza grupo ("S" para sí, cualquier otro valor para no)</param>
        /// <param name="pymDcmTypVal">Tipo de documento de pago (opcional)</param>
        /// <param name="pymDcmVal">Número de documento de pago (opcional)</param>
        /// <param name="thirdPartyDocumentType">Tipo de documento de tercero (opcional, no implementado)</param>
        /// <param name="thirdPartyDocumentCode">Código de documento de tercero (opcional, no implementado)</param>
        /// <returns>DataTable con la información de los documentos de pago encontrados</returns>
        public DataTable ObtenerDocumentoPago(string policyNumber = null,
                                              string initialValidity = null,
                                              string isGroupPolicy = null,
                                              string pymDcmTypVal = null,
                                              string pymDcmVal = null,
                                              string thirdPartyDocumentType = null,
                                              string thirdPartyDocumentCode = null)
        {
            try
            {
                // Limpiar cache de información de recibos al inicio de una nueva consulta
                // para evitar datos obsoletos entre diferentes consultas
                ReciboConverter.LimpiarCacheInfoRecibo();
                // Si se especifica un documento específico, buscarlo directamente
                if (!string.IsNullOrWhiteSpace(pymDcmVal))
                {
                    return ObtenerReciboAviso(pymDcmVal, pymDcmTypVal);
                }

                // Si es póliza grupo, procesar como tal
                if (isGroupPolicy == "S")
                {
                    return ObtenerDocumentoPagoPolizaGrupo(policyNumber);
                }

                // Procesar como póliza individual
                if (!string.IsNullOrWhiteSpace(policyNumber))
                {
                    return ObtenerDoctoPagoPoliza(policyNumber, initialValidity);
                }

                Console.WriteLine("No se proporcionaron criterios suficientes para la búsqueda");
                return new DataTable();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener documentos de pago: {ex.Message}");
                return new DataTable();
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Identifica si una póliza es de tipo grupo basándose en sus suplementos
        /// </summary>
        /// <param name="suplementos">Lista de suplementos de la póliza</param>
        /// <returns>El suplemento que identifica la póliza como grupo, o null si no es póliza grupo</returns>
        private OPlyGniS IdentificarPolizaGrupo(List<OPlyGniS> suplementos)
        {
            return suplementos?.FirstOrDefault(x =>
                !string.IsNullOrEmpty(x.delVal) &&
                !string.IsNullOrEmpty(x.gppVal) &&
                !string.IsNullOrEmpty(x.gppSqnVal));
        }

        /// <summary>
        /// Procesa una póliza grupo y retorna su información en formato DataTable,
        /// recuperando las distintas vigencias y tomando el máximo suplemento cuando se repitan
        /// </summary>
        /// <param name="polizaGrupo">Información del suplemento que identifica la póliza grupo</param>
        /// <returns>DataTable con información de la póliza grupo o null si no se puede procesar</returns>
        private DataTable ProcesarPolizaGrupo(OPlyGniS polizaGrupo)
        {
            try
            {
                Console.WriteLine($"Procesando póliza grupo: {polizaGrupo.gppVal}");

                // Consultar información específica de la póliza grupo
                var polizasGrupo = _polizaGrupoService.ConsultarSuplementosPolizaGrupo(polizaGrupo.gppVal);
                if (polizasGrupo == null || !polizasGrupo.Any())
                {
                    Console.WriteLine($"No se encontraron suplementos para la póliza grupo: {polizaGrupo.gppVal}");
                    return null;
                }

                Console.WriteLine($"Encontrados {polizasGrupo.Count} suplementos para la póliza grupo");

                // Obtener las distintas vigencias de la póliza grupo, tomando el máximo suplemento de cada vigencia
                var vigenciasPolizaGrupo = ObtenerVigenciasPolizaGrupo(polizasGrupo);
                if (!vigenciasPolizaGrupo.Any())
                {
                    Console.WriteLine($"No se encontraron vigencias válidas para la póliza grupo: {polizaGrupo.gppVal}");
                    return null;
                }

                Console.WriteLine($"Procesando {vigenciasPolizaGrupo.Count} vigencias para la póliza grupo");
                if (polizaGrupo.delVal == null)
                {
                    try
                    {
                        var infoPolizaGrupo = vigenciasPolizaGrupo.FirstOrDefault();
                        //polizaGrupo.delVal = infoPolizaGrupo.GppVal; //NUMERO DE CONTRATO
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                }
                // Consultar deals de la póliza grupo
                var dealsPolizaGrupo = _polizaGrupoService.ConsultarDealsPolizaGrupo(polizaGrupo.gppVal, polizaGrupo.delVal);

                // Convertir a DataTable usando solo las vigencias (máximos suplementos)
                return _polizaGrupoService.ConvertPolizaGrupoToDataTable(vigenciasPolizaGrupo, dealsPolizaGrupo);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar póliza grupo {polizaGrupo.gppVal}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Obtiene las vigencias de una póliza grupo, excluyendo suplementos cancelados y tomando el máximo suplemento de cada vigencia
        /// </summary>
        /// <param name="suplementosPolizaGrupo">Lista de suplementos de la póliza grupo</param>
        /// <returns>Lista de suplementos que representan las vigencias de la póliza grupo (máximo suplemento por vigencia)</returns>
        private List<PolizaGrupoStructure> ObtenerVigenciasPolizaGrupo(List<PolizaGrupoStructure> suplementosPolizaGrupo)
        {
            if (suplementosPolizaGrupo == null || !suplementosPolizaGrupo.Any())
                return new List<PolizaGrupoStructure>();

            try
            {
                // Filtrar suplementos válidos (no cancelados) - asumiendo que existe una propiedad similar
                // Si no existe la propiedad de cancelación, usar todos los suplementos
                var suplementosValidos = suplementosPolizaGrupo.ToList();

                // Agrupar por vigencia (fechas de inicio y fin) y obtener el máximo suplemento de cada una
                var suplementosPorVigencia = suplementosValidos
                    .GroupBy(s => new { FechaInicio = s.EnrEfcDat, FechaFin = s.EnrExpDat })
                    .Select(g => new
                    {
                        Vigencia = g.Key,
                        MaximoSuplemento = g.OrderByDescending(s => s.GppEnrSqn).First(),
                        CantidadSuplementos = g.Count()
                    })
                    .ToList();

                Console.WriteLine($"Agrupadas {suplementosPorVigencia.Count} vigencias distintas:");
                foreach (var vigencia in suplementosPorVigencia)
                {
                    var fechaInicio = Utils.UnixTimeStampToDateTime(vigencia.Vigencia.FechaInicio);
                    var fechaFin = Utils.UnixTimeStampToDateTime(vigencia.Vigencia.FechaFin);
                    Console.WriteLine($"  Vigencia {fechaInicio:dd/MM/yyyy} - {fechaFin:dd/MM/yyyy}: " +
                                    $"Máximo suplemento {vigencia.MaximoSuplemento.GppEnrSqn} " +
                                    $"({vigencia.CantidadSuplementos} suplementos en total)");
                }

                return suplementosPorVigencia.Select(s => s.MaximoSuplemento).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener vigencias de póliza grupo: {ex.Message}");
                return new List<PolizaGrupoStructure>();
            }
        }

        /// <summary>
        /// Procesa una póliza individual y retorna su información en formato DataTable
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="suplementosPoliza">Lista de suplementos de la póliza</param>
        /// <returns>DataTable con información de la póliza individual</returns>
        private DataTable ProcesarPolizaIndividual(string policyNumber, List<OPlyGniS> suplementosPoliza)
        {
            var dtInformacionPoliza = new DataTable();

            try
            {
                var vigenciasPoliza = ObtenerVigenciasPoliza(suplementosPoliza);
                if (!vigenciasPoliza.Any())
                {
                    Console.WriteLine($"No se encontraron vigencias válidas para la póliza: {policyNumber}");
                    return dtInformacionPoliza;
                }

                foreach (var vigencia in vigenciasPoliza)
                {
                    var infoPolizaSpto = _polizaService.ConsultarInformacionPolizaPorSuplemento(policyNumber, vigencia.enrSqn);

                    if (infoPolizaSpto != null)
                    {
                        var dtVigenciaPoliza = _polizaService.ConvertPolizaToDataTable(infoPolizaSpto);
                        MergeDataTableSafely(dtInformacionPoliza, dtVigenciaPoliza);
                    }
                }

                return dtInformacionPoliza;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar póliza individual {policyNumber}: {ex.Message}");
                return dtInformacionPoliza;
            }
        }

        /// <summary>
        /// Obtiene las vigencias de la póliza, excluyendo los suplementos anulados y tomando el máximo suplemento de cada vigencia
        /// </summary>
        /// <param name="suplementos">Lista de suplementos de la póliza</param>
        /// <returns>Lista de suplementos que representan las vigencias de la póliza</returns>
        private List<OPlyGniS> ObtenerVigenciasPoliza(List<OPlyGniS> suplementos)
        {
            if (suplementos == null || !suplementos.Any())
                return new List<OPlyGniS>();

            try
            {
                // Filtrar suplementos válidos (no cancelados)
                var suplementosValidos = suplementos.Where(s => s.canEnr == "N").ToList();

                // Agrupar por vigencia y obtener el máximo suplemento de cada una
                var suplementosPorVigencia = suplementosValidos
                    .GroupBy(s => new { FechaInicio = s.plyEfcDat, FechaFin = s.plyExpDat })
                    .Select(g => new
                    {
                        Vigencia = g.Key,
                        MaximoSuplemento = g.OrderByDescending(s => s.enrSqn).First()
                    })
                    .ToList();

                return suplementosPorVigencia.Select(s => s.MaximoSuplemento).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener vigencias de póliza: {ex.Message}");
                return new List<OPlyGniS>();
            }
        }

        /// <summary>
        /// Obtiene la lista de monedas del sistema
        /// </summary>
        /// <returns>Lista de monedas o null si hay error</returns>
        private List<MonedaStructure> ObtenerMonedas()
        {
            try
            {
                return _monedaService.ConsultaMonedas();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener monedas: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Procesa un recibo específico y lo convierte a DataTable
        /// </summary>
        /// <param name="pymDcmVal">Código del documento de recibo</param>
        /// <param name="monedas">Lista de monedas para la conversión</param>
        /// <returns>DataTable con información del recibo o null si no se encuentra</returns>
        private DataTable ProcesarRecibo(string pymDcmVal, List<MonedaStructure> monedas)
        {
            try
            {
                var reciboInformacion = _reciboService.ConsultaInformacionRecibo(pymDcmVal);
                if (reciboInformacion != null)
                {
                    TerceroService terceroService = new TerceroService();
                    //var infoPagador = terceroService.ConsultarResponsablePago(codActTercero:reciboInformacion.oRcpPmiPT, codDocum, tipDocum);
                    return _reciboService.ConvertReciboToDataTable(reciboInformacion, monedas);
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar recibo {pymDcmVal}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Procesa un aviso de pago específico y lo convierte a DataTable
        /// </summary>
        /// <param name="pymDcmVal">Código del documento de aviso de pago</param>
        /// <param name="monedas">Lista de monedas para la conversión</param>
        /// <returns>DataTable con información del aviso de pago o null si no se encuentra</returns>
        private DataTable ProcesarAvisoPago(string pymDcmVal, List<MonedaStructure> monedas)
        {
            try
            {
                var avisoPagoInformacion = _avisoPagoService.ConsultaInformacionAvisoPago(AVISO_PAGO_TYPE_AB, pymDcmVal);
                if (avisoPagoInformacion != null)
                {
                    TerceroService terceroService = new TerceroService();
                    var infoPagador = terceroService.ConsultarResponsablePago(codActTercero: avisoPagoInformacion.thpAcvVal.ToString(),
                                                                              codDocum: avisoPagoInformacion.thpDcmVal,
                                                                              tipDocum: avisoPagoInformacion.thpDcmTypVal);

                    return _avisoPagoService.ConvertAvisoPagoToDataTable(avisoPagoInformacion, monedas, infoPagador);
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar aviso de pago {pymDcmVal}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Procesa los documentos de pago para una póliza individual, opcionalmente filtrando por fecha de inicio de vigencia
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="initialValidity">Fecha de inicio de vigencia en formato dd/MM/yyyy (opcional)</param>
        /// <returns>DataTable con información de los documentos de pago</returns>
        private DataTable ObtenerDoctoPagoPoliza(string policyNumber, string initialValidity)
        {
            var dtInformacionRecibos = new DataTable();

            try
            {
                List<ReciboStructure> recibos = new List<ReciboStructure>();

                if (!string.IsNullOrWhiteSpace(policyNumber) && !string.IsNullOrWhiteSpace(initialValidity))
                {
                    // Procesar con fecha de vigencia específica para obtener el máximo suplemento
                    recibos = ObtenerRecibosPorVigenciaEspecifica(policyNumber: policyNumber,
                                                                  initialValidity: initialValidity);
                }
                else if (!string.IsNullOrWhiteSpace(policyNumber))
                {
                    // Consultar recibos de la póliza con parámetros por defecto
                    recibos = _reciboService.ConsultarRecibosPorPoliza(policyNumber: policyNumber,
                                                                       aplEnrSqn: 0,
                                                                       aplVal: 0,
                                                                       enrSqn: 0);
                }

                if (recibos == null || !recibos.Any())
                {
                    Console.WriteLine($"No se encontraron recibos para la póliza: {policyNumber}");
                    return dtInformacionRecibos;
                }

                // Obtener monedas una sola vez
                var monedas = ObtenerMonedas();
                if (monedas == null)
                {
                    Console.WriteLine("No se pudieron obtener las monedas del sistema");
                    return dtInformacionRecibos;
                }

                foreach (var recibo in recibos)
                {
                    // Verificar si el recibo pertenece a un aviso de pago y ya está en el DataTable
                    if (EsReciboDeAvisoPagoYaExistente(recibo, dtInformacionRecibos))
                    {
                        Console.WriteLine($"Recibo con aviso de pago ya procesado, omitiendo duplicado");
                        continue;
                    }

                    var dtDocumentoPago = ProcesarDocumentoPagoIndividual(recibo, monedas);
                    if (dtDocumentoPago != null && dtDocumentoPago.Rows.Count > 0)
                    {
                        MergeDataTableSafely(dtInformacionRecibos, dtDocumentoPago);
                    }
                }

                return dtInformacionRecibos;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar documentos de pago para póliza {policyNumber}: {ex.Message}");
                return dtInformacionRecibos;
            }
        }

        /// <summary>
        /// Procesa un documento de pago individual (recibo o aviso de pago)
        /// </summary>
        /// <param name="recibo">Información del recibo</param>
        /// <param name="monedas">Lista de monedas</param>
        /// <returns>DataTable con información del documento de pago</returns>
        private DataTable ProcesarDocumentoPagoIndividual(ReciboStructure recibo, List<MonedaStructure> monedas)
        {
            try
            {
                var pmrS = recibo.oRcpErcC?.oRcpPmrP?.oRcpPmrS;
                if (pmrS == null)
                {
                    return null;
                }

                // Verificar si tiene aviso de pago válido
                if (!string.IsNullOrEmpty(pmrS.pymDcmVal))
                {
                    var avisoPago = _avisoPagoService.ConsultaInformacionAvisoPago(pmrS.pymDcmTypVal, pmrS.pymDcmVal);
                    if (avisoPago != null && avisoPago.dcmAmn > 0)
                    {
                        TerceroService terceroService = new TerceroService();
                        var infoPagador = terceroService.ConsultarResponsablePago(codActTercero: avisoPago.thpAcvVal.ToString(),
                                                                                  codDocum: avisoPago.thpDcmVal,
                                                                                  tipDocum: avisoPago.thpDcmTypVal);

                        return _avisoPagoService.ConvertAvisoPagoToDataTable(avisoPago, monedas, infoPagador);
                    }
                }

                // Si no hay aviso de pago válido, procesar como recibo
                if (pmrS.rcpAmn > 0)
                {
                    return _reciboService.ConvertReciboToDataTable(recibo.oRcpErcC, monedas);
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al procesar documento de pago individual: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Obtiene los recibos de una póliza para una vigencia específica, utilizando el máximo suplemento de esa vigencia
        /// </summary>
        /// <param name="policyNumber">Número de póliza</param>
        /// <param name="initialValidity">Fecha de inicio de vigencia en formato dd/MM/yyyy</param>
        /// <returns>Lista de recibos para la vigencia específica</returns>
        private List<ReciboStructure> ObtenerRecibosPorVigenciaEspecifica(string policyNumber, string initialValidity)
        {
            try
            {
                // Parsear la fecha de inicio de vigencia
                var fechaInicioVigencia = DateTime.ParseExact(initialValidity, "dd/MM/yyyy", CultureInfo.InvariantCulture);

                // Obtener todos los suplementos de la póliza
                var polizaSptos = _polizaService.ConsultarSuplementosPoliza(policyNumber);
                if (polizaSptos == null || !polizaSptos.Any())
                {
                    Console.WriteLine($"No se encontraron suplementos para la póliza: {policyNumber}");
                    return new List<ReciboStructure>();
                }

                // Buscar el máximo suplemento para la vigencia específica
                var polizaVigencia = polizaSptos.Where(x => Utils.UnixTimeStampToDateTime(x.plyEfcDat).Date == fechaInicioVigencia.Date)
                                                .GroupBy(s => new
                                                {
                                                    plyEfcDat = s.plyEfcDat,
                                                    plyExpDat = s.plyExpDat
                                                })
                                                .Select(g => new
                                                {
                                                    Vigencia = g.Key,
                                                    MaxSpto = g.OrderByDescending(s => s.enrSqn).First(),
                                                    MinSpto = g.OrderBy(s => s.enrSqn).First()
                                                })
                                                .FirstOrDefault();

                if (polizaVigencia == null)
                {
                    Console.WriteLine($"No se encontró vigencia para la fecha {initialValidity} en la póliza: {policyNumber}");
                    return new List<ReciboStructure>();
                }

                // Obtener recibos usando los parámetros del máximo suplemento de la vigencia
                var recibos = _reciboService.ConsultarRecibosPorPoliza(policyNumber: policyNumber,
                                                                       aplEnrSqn: polizaVigencia.MaxSpto.aplEnrSqn,
                                                                       aplVal: polizaVigencia.MaxSpto.aplVal,
                                                                       enrSqn: polizaVigencia.MaxSpto.enrSqn);

                if (recibos == null)
                {
                    return new List<ReciboStructure>();
                }

                var recibosFiltrados = recibos.Where(r =>
                                                     r.oRcpErcC?.oRcpPmiPT?.FirstOrDefault()?.oRcpPmiS?.enrSqn >= polizaVigencia.MinSpto.enrSqn &&
                                                     r.oRcpErcC?.oRcpPmiPT?.FirstOrDefault()?.oRcpPmiS?.enrSqn <= polizaVigencia.MaxSpto.enrSqn)
                                              .ToList();

                return recibosFiltrados;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener recibos por vigencia específica para póliza {policyNumber}: {ex.Message}");
                return new List<ReciboStructure>();
            }
        }

        /// <summary>
        /// Obtiene los documentos de pago (avisos de pago) para una póliza grupo
        /// </summary>
        /// <param name="policyNumber">Número de póliza grupo en formato "numContrato-numPolizaGrupo" o solo numPolizaGrupo</param>
        /// <returns>DataTable con información de los avisos de pago de la póliza grupo</returns>
        private DataTable ObtenerDocumentoPagoPolizaGrupo(string policyNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(policyNumber))
                {
                    Console.WriteLine("Número de póliza grupo no proporcionado");
                    return new DataTable();
                }

                string numContrato = null;

                var avisosPago = _avisoPagoService.ConsultarAvisosPagoPolizaGrupo(numContrato, policyNumber);
                if (avisosPago == null || !avisosPago.Any())
                {
                    Console.WriteLine($"No se encontraron avisos de pago para la póliza grupo: {policyNumber}");
                    return new DataTable();
                }

                Console.WriteLine($"Encontrados {avisosPago.Count} avisos de pago para la póliza grupo");

                // Obtener monedas para la conversión
                var monedas = ObtenerMonedas();
                if (monedas == null)
                {
                    Console.WriteLine("No se pudieron obtener las monedas del sistema");
                    return new DataTable();
                }

                // Convertir a DataTable
                return _avisoPagoService.ConvertAvisosPagoPolizaGrupoToDataTable(avisosPago, monedas);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener documentos de pago para póliza grupo {policyNumber}: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// Verifica si un recibo pertenece a un aviso de pago y ya existe en el DataTable
        /// </summary>
        /// <param name="recibo">Recibo a verificar</param>
        /// <param name="dtInformacionRecibos">DataTable con los recibos ya procesados</param>
        /// <returns>True si el recibo pertenece a un aviso de pago y ya está en el DataTable</returns>
        private bool EsReciboDeAvisoPagoYaExistente(ReciboStructure recibo, DataTable dtInformacionRecibos)
        {
            try
            {
                var pmrS = recibo.oRcpErcC?.oRcpPmrP?.oRcpPmrS;
                if (pmrS == null)
                {
                    return false;
                }

                // Verificar si el recibo tiene un aviso de pago asociado
                if (string.IsNullOrEmpty(pmrS.pymDcmVal))
                {
                    return false; // No es un recibo con aviso de pago
                }

                // Si no hay filas en el DataTable, no puede estar duplicado
                if (dtInformacionRecibos.Rows.Count == 0)
                {
                    return false;
                }

                // Buscar si ya existe un registro con el mismo número de aviso de pago
                foreach (DataRow row in dtInformacionRecibos.Rows)
                {
                    // Verificar si es un aviso de pago (ES_AVISO = "S") y tiene el mismo REQUERIMIENTO
                    if (row["ES_AVISO"] != null &&
                        row["ES_AVISO"].ToString() == "S" &&
                        row["REQUERIMIENTO"] != null &&
                        row["REQUERIMIENTO"].ToString() == pmrS.pymDcmVal)
                    {
                        return true; // Ya existe el aviso de pago en el DataTable
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al verificar duplicado de aviso de pago: {ex.Message}");
                return false; // En caso de error, permitir el procesamiento
            }
        }

        /// <summary>
        /// Realiza un merge seguro entre dos DataTables, manejando casos donde el destino esté vacío
        /// </summary>
        /// <param name="destino">DataTable destino</param>
        /// <param name="origen">DataTable origen a fusionar</param>
        private void MergeDataTableSafely(DataTable destino, DataTable origen)
        {
            if (origen == null || origen.Rows.Count == 0)
                return;

            try
            {
                if (destino.Columns.Count == 0)
                {
                    // Si el destino está vacío, clonamos la estructura del origen
                    foreach (DataColumn column in origen.Columns)
                    {
                        destino.Columns.Add(column.ColumnName, column.DataType);
                    }
                }

                destino.Merge(origen);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al fusionar DataTables: {ex.Message}");
            }
        }

        #endregion
    }
}
